{"permissions": {"allow": ["mcp__zen__thinkdeep", "Bash(find:*)", "<PERSON><PERSON>(uv run:*)", "Bash(mcp add:*)", "Bash(npm install:*)", "Bash(npx:*)", "mcp__serena__get_current_config", "mcp__serena__initial_instructions", "WebFetch(domain:www.vnpy.com)", "mcp__serena__list_dir", "mcp__serena__read_file", "mcp__serena__get_symbols_overview", "mcp__serena__find_symbol", "mcp__zen__analyze", "Bash(grep:*)", "<PERSON><PERSON>(source:*)", "Bash(pip install:*)", "Bash(ruff check:*)", "Bash(ruff format:*)", "Bash(mcp remove:*)", "Bash(claude-code mcp:*)", "<PERSON><PERSON>(claude mcp:*)", "<PERSON><PERSON>(python:*)", "Bash(rm:*)", "mcp__zen__challenge", "Bash(ls:*)", "<PERSON><PERSON>(cat:*)"], "deny": []}}