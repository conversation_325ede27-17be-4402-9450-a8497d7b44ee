#!/usr/bin/env python3
"""
交易机器人系统

提供多种交易机器人的实现，支持纸上交易和真实交易模式。
包含订单队列管理、风险控制、策略执行等核心功能。
支持异步交易执行和多机器人并发运行。
"""

import asyncio
import logging
import os
import random
import time
from datetime import datetime

import numpy as np

logger = logging.getLogger(__name__)

# 导入IBKR客户端用于真实交易
try:
    from core.broker import get_ibkr_client
    from config import DEFAULT_CONFIG, UnifiedConfig
    from config import IBKRConfig
    from core.exceptions import (
        TradingSystemError,
        IBKRConnectionError,
        DataValidationError,
        ErrorCategory,
        handle_errors,
        async_handle_errors,
        OperationResult,
        safe_execute
    )

    IBKR_AVAILABLE = True
except ImportError:
    IBKR_AVAILABLE = False


class Bot:
    """
    这是一个具有可选真实交易功能的通用机器人结构。
    """

    def __init__(
        self,
        capital: float,
        portfolio: dict = None,
        real_trading: bool = False,
        ibkr_config: IBKRConfig = None,
    ):
        self.capital = capital
        self.uninvested = capital
        self.invested = 0
        self.portfolio = {} if portfolio is None else portfolio

        # 真实交易设置
        self.real_trading = real_trading and IBKR_AVAILABLE
        self.ibkr_client = None

        # 处理配置对象 - 确保获取正确的IBKR配置
        if ibkr_config is None:
            self.ibkr_config = DEFAULT_CONFIG.ibkr
        elif hasattr(ibkr_config, 'ibkr'):
            # 如果传入的是UnifiedConfig，提取IBKR配置
            self.ibkr_config = ibkr_config.ibkr
        else:
            # 如果传入的是IBKRConfig，直接使用
            self.ibkr_config = ibkr_config
        self.trade_log = []

        if self.real_trading:
            result = safe_execute(
                self._initialize_ibkr_client,
                log_errors=True
            )
            if not result.success:
                print(f"初始化 IBKR 客户端失败: {result.error.message}")
                self.ibkr_client = None

    def _initialize_ibkr_client(self):
        """初始化IBKR客户端"""
        # 使用统一的客户端管理器获取交易专用客户端
        self.ibkr_client = get_ibkr_client("trading", self.ibkr_config)
        print(
            f"Bot initialized for {'PAPER' if self.ibkr_config.paper_trading else 'LIVE'} trading"
        )
        print(f"使用统一管理的交易客户端ID: {self.ibkr_client.config.client_id}")

    @async_handle_errors(default_return=False, error_category=ErrorCategory.CONNECTION)
    async def connect_to_ibkr(self) -> bool:
        """连接到IBKR进行真实交易"""
        if not self.real_trading or not self.ibkr_client:
            return False

        # 检查是否已经连接
        if hasattr(self.ibkr_client, "connected") and self.ibkr_client.connected:
            print("✅ IBKR 客户端已连接")
            return True

        print(
            f"🔄 Connecting to IBKR for {'PAPER' if self.ibkr_config.paper_trading else 'LIVE'} trading..."
        )

        # 尝试连接，最多重试3次
        max_retries = 3
        for attempt in range(max_retries):
            try:
                result = await self.ibkr_client.connect()
                if isinstance(result, OperationResult):
                    if result.success:
                        return result
                elif result:  # 如果返回True或其他真值
                    return OperationResult.success_result(data=result)
            except Exception as e:
                logger.error(f"IBKR连接尝试 {attempt + 1} 失败: {e}")
                result = OperationResult.error_result(error=TradingSystemError(
                    message=f"IBKR连接失败: {str(e)}",
                    category=ErrorCategory.CONNECTION,
                    severity=ErrorSeverity.HIGH
                ))

            if not result.success:
                error_msg = result.error.message if result.error else "连接失败"
                print(f"❌ 连接 IBKR 失败 (第{attempt + 1}次尝试): {error_msg}")
                if attempt < max_retries - 1:
                    print("⏳ 重试前等待 5 秒...")
                    await asyncio.sleep(5)
            else:
                print(f"✅ 成功连接到 IBKR (第{attempt + 1}次尝试)")
                return True

        print(f"❌ 经过 {max_retries} 次尝试后仍无法连接到 IBKR")
        raise IBKRConnectionError(
            "无法连接到IBKR",
            details={"max_retries": max_retries}
        )

    def disconnect_from_ibkr(self):
        """断开与IBKR的连接"""
        if self.real_trading and self.ibkr_client:
            self.ibkr_client.disconnect()

    @handle_errors(reraise=True)
    def transact_capital(self, ticker, units: int, price: float, type: str) -> OperationResult:
        """执行交易（模拟或真实）"""
        # 参数验证
        if not ticker:
            raise DataValidationError("股票不能为空")

        if type not in ["sell", "buy"]:
            raise DataValidationError(
                f"交易类型 {type} 不被识别。请选择 'sell' 或 'buy'"
            )

        if units <= 0:
            raise DataValidationError(f"交易数量必须大于0，当前值: {units}")

        if price <= 0:
            raise DataValidationError(f"交易价格必须大于0，当前值: {price}")

        # 记录交易
        trade_record = {
            "timestamp": datetime.now(),
            "ticker": ticker,
            "units": units,
            "price": price,
            "type": type,
            "real_trade": self.real_trading,
        }

        # 如果启用，执行真实交易
        if self.real_trading and self.ibkr_client:
            try:
                # 这将在真实交易循环中异步调用
                self._queue_real_trade(ticker, units, price, type)
            except Exception as e:
                print(f"{ticker}实盘交易失败: {e}")
                trade_record["error"] = str(e)

        # 始终记录交易
        if not hasattr(self, "trade_log"):
            self.trade_log = []

        # 无论如何都要更新模拟投资组合
        if type == "sell":
            transaction = units * price
            self.uninvested += transaction
            if ticker in self.portfolio:
                del self.portfolio[ticker]

        elif type == "buy":
            transaction = units * price
            self.uninvested -= transaction
            self.portfolio[ticker] = {"units": units, "purchase_price": price}

        # 记录交易
        self.trade_log.append(trade_record)

        # 根据IBKR端口确定模式
        if self.ibkr_config and hasattr(self.ibkr_config, "port"):
            mode = "LIVE" if self.ibkr_config.port == 7496 else "PAPER"
        else:
            mode = "REAL" if self.real_trading else "SIM"

        print(f"{mode} {type.upper()}: {units}股{ticker}，价格${price:.2f}")

    def _queue_real_trade(self, ticker: str, units: int, price: float, trade_type: str):
        """将真实交易加入执行队列 (异步调用)"""
        if not hasattr(self, "_trade_queue"):
            self._trade_queue = []

        self._trade_queue.append(
            {"ticker": ticker, "units": units, "price": price, "type": trade_type}
        )

    async def execute_queued_trades(self):
        """执行所有队列中的真实交易"""
        if not self.real_trading or not hasattr(self, "_trade_queue"):
            print("📊 未启用实盘交易或没有交易队列")
            return

        if not self._trade_queue:
            print("📊 没有排队等待执行的交易")
            return

        print(f"🚀 执行 {len(self._trade_queue)} 个排队交易...")

        # 执行交易前检查IBKR连接
        if not self.ibkr_client or not self.ibkr_client.connected:
            print("❌ 无法执行交易: IBKR 客户端未连接")
            print(f"   实盘交易: {self.real_trading}")
            print(f"   IBKR 客户端存在: {self.ibkr_client is not None}")
            if self.ibkr_client:
                print(f"   IBKR 客户端已连接: {self.ibkr_client.connected}")
            return

        executed_trades = 0
        failed_trades = 0
        trade_queue_copy = self._trade_queue.copy()  # 创建副本以避免修改原队列时的问题
        self._trade_queue.clear()  # 清空原队列

        for trade in trade_queue_copy:
            try:
                action = "BUY" if trade["type"] == "buy" else "SELL"

                # 验证交易参数
                if trade["units"] <= 0:
                    print(
                        f"❌ Invalid trade units: {trade['units']} for {trade['ticker']}"
                    )
                    failed_trades += 1
                    continue

                if trade["price"] <= 0:
                    print(
                        f"❌ Invalid trade price: {trade['price']} for {trade['ticker']}"
                    )
                    failed_trades += 1
                    continue

                print(
                    f"🔄 Executing {action} order: {trade['units']} shares of {trade['ticker']} @ ${trade['price']:.2f}"
                )

                # 目前使用市价单（可以改进为限价单）
                result = await self.ibkr_client.place_market_order(
                    trade["ticker"], trade["units"], action
                )

                if result:
                    print(
                        f"✅ Real trade executed: {action} {trade['units']} {trade['ticker']} @ ${trade['price']:.2f}"
                    )
                    executed_trades += 1

                    # 用执行状态更新交易日志
                    trade["executed"] = True
                    trade["execution_time"] = datetime.now()
                    trade["order_id"] = getattr(result, "orderId", None)
                    self.trade_log.append(trade)
                else:
                    print(
                        f"❌ Real trade failed: {action} {trade['units']} {trade['ticker']}"
                    )
                    failed_trades += 1

                    # 记录失败的交易
                    trade["executed"] = False
                    trade["execution_time"] = datetime.now()
                    trade["error"] = "Execution failed - no result returned"
                    self.trade_log.append(trade)

                # 在交易之间添加小延迟以避免速率限制
                await asyncio.sleep(0.5)

            except Exception as e:
                print(f"❌ 执行实盘交易 {trade['ticker']} 错误: {e}")
                failed_trades += 1

                # 记录错误
                trade["executed"] = False
                trade["execution_time"] = datetime.now()
                trade["error"] = str(e)
                self.trade_log.append(trade)

        # 汇总
        total_trades = executed_trades + failed_trades
        if total_trades > 0:
            print(
                f"📊 Trade execution summary: {executed_trades}/{total_trades} successful"
            )
            if executed_trades > 0:
                print(f"✅ 成功执行 {executed_trades} 个实盘交易")
            if failed_trades > 0:
                print(f"❌ 执行 {failed_trades} 个交易失败")
        else:
            print("📊 No trades were processed")

    def compute_capital(self, price: dict):
        self.invested = 0.0
        for ticker in self.portfolio:
            self.invested += self.portfolio[ticker]["units"] * price[ticker]
        self.capital = self.uninvested + self.invested

    async def analyze_and_trade(self, trading_signals: dict) -> dict:
        """
        分析市场信号并做出交易决策
        
        Args:
            trading_signals: 包含市场信号的字典，格式为：
            {
                "extreme_above_trend": [股票列表],
                "highly_above_trend": [股票列表],
                "extreme_below_trend": [股票列表],
                "highly_below_trend": [股票列表],
                "along_trend": [股票列表],
                "timestamp": datetime,
                "market_summary": {...}
            }
        
        Returns:
            dict: 交易决策结果，包含执行的交易和组合状态
        """
        logger.info(f"{self.__class__.__name__} 开始分析市场信号...")
        
        # 默认实现：调用原有的trade方法
        # 子类应该重写此方法以实现具体的策略逻辑
        trading_info = self._convert_signals_to_legacy_format(trading_signals)
        
        if trading_info:
            logger.info(f"发现 {len(trading_info)} 个交易机会")
            self.trade(trading_info)
            
            # 执行队列中的真实交易
            if hasattr(self, "execute_queued_trades"):
                if hasattr(self, "connect_to_ibkr"):
                    connected = await self.connect_to_ibkr()
                    if connected:
                        await self.execute_queued_trades()
                else:
                    await self.execute_queued_trades()
            
            return {
                "trades_executed": len([t for t in self.trade_log if t.get("timestamp") and 
                                     (datetime.now() - t["timestamp"]).seconds < 60]),
                "current_prices": {ticker: info["price"] for ticker, info in trading_info.items()},
                "strategy": "default",
                "decision_factors": "Legacy trade method"
            }
        else:
            logger.info("未发现符合条件的交易机会")
            return {}

    def _convert_signals_to_legacy_format(self, trading_signals: dict) -> dict:
        """将新的信号格式转换为旧的trading_info格式"""
        trading_info = {}
        
        # 合并所有信号类别
        all_categories = ["extreme_above_trend", "highly_above_trend", "extreme_below_trend", 
                         "highly_below_trend", "along_trend", "above_trend", "below_trend"]
        
        for category in all_categories:
            stocks = trading_signals.get(category, [])
            for stock in stocks:
                trading_info[stock["ticker"]] = {
                    "price": stock["current_price"],
                    "rate": category.upper().replace("_", " "),
                    "growth": (stock["predicted_price"] - stock["current_price"]) / stock["current_price"],
                    "score": stock["score"],
                }
        
        return trading_info


class Adam(Bot):
    def __init__(
        self,
        capital: float,
        portfolio: dict = None,
        real_trading: bool = False,
        ibkr_config=None,
    ):
        super().__init__(capital, portfolio, real_trading, ibkr_config)
        self.min_rel_profit = 0.03
        self.max_rel_loss = 0.1

    async def analyze_and_trade(self, trading_signals: dict) -> dict:
        """
        Adam - 保守价值投资者
        专注于极度低估的股票，追求稳定回报
        """
        logger.info("Adam (保守价值投资者) 分析市场信号...")
        
        # Adam的策略：优先考虑极度低估的股票
        target_stocks = []
        decision_factors = []

        # 第一优先级：极度低估股票（罕见机会）
        extreme_below = trading_signals.get("extreme_below_trend", [])
        if extreme_below:
            # 选择评分最低的前3只（最被低估）
            top_extreme = sorted(extreme_below, key=lambda x: x["score"])[:3]
            target_stocks.extend(top_extreme)
            decision_factors.append(f"极度低估机会: {len(top_extreme)}只")
            logger.info(f"💎 发现 {len(top_extreme)} 只极度低估股票")

        # 第二优先级：高度低估股票
        highly_below = trading_signals.get("highly_below_trend", [])
        if highly_below and len(target_stocks) < 5:
            remaining_slots = 5 - len(target_stocks)
            top_highly = sorted(highly_below, key=lambda x: x["score"])[:remaining_slots]
            target_stocks.extend(top_highly)
            decision_factors.append(f"高度低估机会: {len(top_highly)}只")

        # 执行卖出策略（止盈止损）
        trades_executed = 0
        sell_decisions = []
        
        owned_stocks = list(self.portfolio.keys())
        for ticker in owned_stocks:
            # 从所有信号中找到当前价格
            current_price = None
            for category_stocks in trading_signals.values():
                if isinstance(category_stocks, list):
                    for stock in category_stocks:
                        if stock["ticker"] == ticker:
                            current_price = stock["current_price"]
                            break
                if current_price:
                    break
            
            if current_price:
                purchase_price = self.portfolio[ticker]["purchase_price"]
                rel_margin = (current_price - purchase_price) / purchase_price
                
                if rel_margin > self.min_rel_profit:
                    self.transact_capital(ticker, self.portfolio[ticker]["units"], current_price, type="sell")
                    sell_decisions.append(f"{ticker}: 止盈 (+{rel_margin:.1%})")
                    trades_executed += 1
                elif rel_margin < -self.max_rel_loss:
                    self.transact_capital(ticker, self.portfolio[ticker]["units"], current_price, type="sell")
                    sell_decisions.append(f"{ticker}: 止损 ({rel_margin:.1%})")
                    trades_executed += 1

        # 执行买入策略
        buy_decisions = []
        for stock in target_stocks:
            if stock["ticker"] not in self.portfolio:
                # Adam保守策略：每只股票投入总资本的1/30，最大10只股票
                position_size = min(self.uninvested, self.capital / 30)
                units = int(position_size // stock["current_price"])
                
                if units >= 1:
                    self.transact_capital(stock["ticker"], units, stock["current_price"], type="buy")
                    buy_decisions.append(f"{stock['ticker']}: 价值投资 (评分: {stock['score']:.2f})")
                    trades_executed += 1

        # 执行队列中的真实交易
        if hasattr(self, "execute_queued_trades"):
            if hasattr(self, "connect_to_ibkr"):
                connected = await self.connect_to_ibkr()
                if connected:
                    await self.execute_queued_trades()
            else:
                await self.execute_queued_trades()

        # 记录决策因素
        all_decisions = sell_decisions + buy_decisions
        if all_decisions:
            decision_factors.extend(all_decisions)

        logger.info(f"Adam 完成分析: {trades_executed} 笔交易")

        return {
            "trades_executed": trades_executed,
            "current_prices": {stock["ticker"]: stock["current_price"] for stock in target_stocks},
            "strategy": "保守价值投资",
            "decision_factors": "; ".join(decision_factors) if decision_factors else "未发现合适机会",
            "target_stocks": [stock["ticker"] for stock in target_stocks],
            "portfolio_allocation": "1/30 分散投资"
        } if target_stocks or trades_executed > 0 else {}

    def trade(self, info: dict):
        ## 卖出策略
        owned_stocks = list(self.portfolio.keys())
        for ticker in owned_stocks:
            if ticker in info:  # 检查股票数据是否可用
                purchase_price = self.portfolio[ticker]["purchase_price"]
                rel_margin = (info[ticker]["price"] - purchase_price) / purchase_price
                if rel_margin > self.min_rel_profit or rel_margin < -self.max_rel_loss:
                    self.transact_capital(
                        ticker,
                        self.portfolio[ticker]["units"],
                        info[ticker]["price"],
                        type="sell",
                    )
            else:
                logger.warning(f"No price data available for owned stock {ticker}")

        ## 买入策略
        for ticker in info:
            if (
                ticker not in self.portfolio.keys()
                and info[ticker]["rate"] == "HIGHLY BELOW TREND"
            ):
                units = int(
                    np.minimum(self.uninvested, self.capital / 30)
                    // info[ticker]["price"]
                )
                if units >= 1:
                    self.transact_capital(
                        ticker, units, info[ticker]["price"], type="buy"
                    )


class Betty(Bot):
    def __init__(
        self,
        capital: float,
        portfolio: dict = None,
        real_trading: bool = False,
        ibkr_config=None,
    ):
        super().__init__(capital, portfolio, real_trading, ibkr_config)
        self.min_rel_profit = 0.1
        self.max_rel_loss = 0.03

    async def analyze_and_trade(self, trading_signals: dict) -> dict:
        """
        Betty - 激进动量投资者
        专注于强势上涨股票，追求高回报
        """
        logger.info("Betty (激进动量投资者) 分析市场信号...")
        
        # Betty的策略：优先考虑强势上涨的股票
        target_stocks = []
        decision_factors = []

        # 第一优先级：极度强势股票（罕见机会）
        extreme_above = trading_signals.get("extreme_above_trend", [])
        if extreme_above:
            # 选择评分最高的前3只（最强势）
            top_extreme = sorted(extreme_above, key=lambda x: x["score"], reverse=True)[:3]
            target_stocks.extend(top_extreme)
            decision_factors.append(f"极度强势机会: {len(top_extreme)}只")
            logger.info(f"🔥 发现 {len(top_extreme)} 只极度强势股票")

        # 第二优先级：高度强势股票
        highly_above = trading_signals.get("highly_above_trend", [])
        if highly_above and len(target_stocks) < 5:
            remaining_slots = 5 - len(target_stocks)
            top_highly = sorted(highly_above, key=lambda x: x["score"], reverse=True)[:remaining_slots]
            target_stocks.extend(top_highly)
            decision_factors.append(f"高度强势机会: {len(top_highly)}只")

        # 执行卖出策略（严格止损，追求高收益）
        trades_executed = 0
        sell_decisions = []
        
        owned_stocks = list(self.portfolio.keys())
        for ticker in owned_stocks:
            # 从所有信号中找到当前价格
            current_price = None
            for category_stocks in trading_signals.values():
                if isinstance(category_stocks, list):
                    for stock in category_stocks:
                        if stock["ticker"] == ticker:
                            current_price = stock["current_price"]
                            break
                if current_price:
                    break
            
            if current_price:
                purchase_price = self.portfolio[ticker]["purchase_price"]
                rel_margin = (current_price - purchase_price) / purchase_price
                
                if rel_margin > self.min_rel_profit:
                    self.transact_capital(ticker, self.portfolio[ticker]["units"], current_price, type="sell")
                    sell_decisions.append(f"{ticker}: 止盈 (+{rel_margin:.1%})")
                    trades_executed += 1
                elif rel_margin < -self.max_rel_loss:
                    self.transact_capital(ticker, self.portfolio[ticker]["units"], current_price, type="sell")
                    sell_decisions.append(f"{ticker}: 快速止损 ({rel_margin:.1%})")
                    trades_executed += 1

        # 执行买入策略（集中投资强势股）
        buy_decisions = []
        for stock in target_stocks:
            if stock["ticker"] not in self.portfolio:
                # Betty激进策略：每只股票投入总资本的1/20，更集中
                position_size = min(self.uninvested, self.capital / 20)
                units = int(position_size // stock["current_price"])
                
                if units >= 1:
                    self.transact_capital(stock["ticker"], units, stock["current_price"], type="buy")
                    buy_decisions.append(f"{stock['ticker']}: 动量投资 (评分: {stock['score']:.2f})")
                    trades_executed += 1

        # 执行队列中的真实交易
        if hasattr(self, "execute_queued_trades"):
            if hasattr(self, "connect_to_ibkr"):
                connected = await self.connect_to_ibkr()
                if connected:
                    await self.execute_queued_trades()
            else:
                await self.execute_queued_trades()

        # 记录决策因素
        all_decisions = sell_decisions + buy_decisions
        if all_decisions:
            decision_factors.extend(all_decisions)

        logger.info(f"Betty 完成分析: {trades_executed} 笔交易")

        return {
            "trades_executed": trades_executed,
            "current_prices": {stock["ticker"]: stock["current_price"] for stock in target_stocks},
            "strategy": "激进动量投资",
            "decision_factors": "; ".join(decision_factors) if decision_factors else "未发现合适机会",
            "target_stocks": [stock["ticker"] for stock in target_stocks],
            "portfolio_allocation": "1/20 集中投资"
        } if target_stocks or trades_executed > 0 else {}

    def trade(self, info: dict):
        ## 卖出策略
        owned_stocks = list(self.portfolio.keys())
        for ticker in owned_stocks:
            if ticker in info:  # 检查股票数据是否可用
                purchase_price = self.portfolio[ticker]["purchase_price"]
                rel_margin = (info[ticker]["price"] - purchase_price) / purchase_price
                if rel_margin > self.min_rel_profit or rel_margin < -self.max_rel_loss:
                    self.transact_capital(
                        ticker,
                        self.portfolio[ticker]["units"],
                        info[ticker]["price"],
                        type="sell",
                    )
            else:
                logger.warning(f"No price data available for owned stock {ticker}")

        ## 买入策略
        for ticker in info:
            if (
                ticker not in self.portfolio.keys()
                and info[ticker]["rate"] == "HIGHLY ABOVE TREND"
            ):
                units = int(
                    np.minimum(self.uninvested, self.capital / 30)
                    // info[ticker]["price"]
                )
                if units >= 1:
                    self.transact_capital(
                        ticker, units, info[ticker]["price"], type="buy"
                    )


class Chris(Bot):
    def __init__(
        self,
        capital: float,
        portfolio: dict = None,
        real_trading: bool = False,
        ibkr_config=None,
    ):
        super().__init__(capital, portfolio, real_trading, ibkr_config)
        self.buy_only = ["GOOGL", "AMZN", "AAPL", "MSFT", "META"]

    async def analyze_and_trade(self, trading_signals: dict) -> dict:
        """
        Chris - 精选蓝筹股投资者
        只投资特定的大型科技股，长期持有
        """
        logger.info("Chris (精选蓝筹股投资者) 分析市场信号...")
        
        # Chris的策略：只关注指定的蓝筹股
        target_stocks = []
        decision_factors = []

        # 搜索所有信号类别中的目标股票
        all_available_stocks = []
        for category_name, stocks in trading_signals.items():
            if isinstance(stocks, list):
                all_available_stocks.extend(stocks)

        # 筛选Chris关注的股票
        chris_stocks_available = []
        for stock in all_available_stocks:
            if stock["ticker"] in self.buy_only:
                chris_stocks_available.append(stock)

        if chris_stocks_available:
            # 按照评分排序，选择表现最好的Chris股票
            chris_stocks_available.sort(key=lambda x: x["score"], reverse=True)
            target_stocks = chris_stocks_available
            decision_factors.append(f"发现蓝筹股机会: {[stock['ticker'] for stock in target_stocks]}")
            logger.info(f"📈 发现 {len(target_stocks)} 只目标蓝筹股")
        else:
            logger.info("🔍 未发现目标蓝筹股交易机会")

        # Chris不主动卖出，除非基本面发生重大变化
        trades_executed = 0
        sell_decisions = []
        
        # Chris持有策略：长期持有，很少卖出
        logger.info("Chris 采用长期持有策略，不主动卖出")

        # 执行买入策略（平均分配资金）
        buy_decisions = []
        if target_stocks:
            # 计算每只股票的资金分配
            available_positions = len(self.buy_only) - len(self.portfolio)
            if available_positions > 0:
                funds_per_stock = self.uninvested / available_positions
                
                for stock in target_stocks:
                    if stock["ticker"] not in self.portfolio:
                        units = int(funds_per_stock // stock["current_price"])
                        
                        if units >= 1:
                            self.transact_capital(stock["ticker"], units, stock["current_price"], type="buy")
                            buy_decisions.append(f"{stock['ticker']}: 蓝筹投资 (评分: {stock['score']:.2f})")
                            trades_executed += 1

        # 执行队列中的真实交易
        if hasattr(self, "execute_queued_trades"):
            if hasattr(self, "connect_to_ibkr"):
                connected = await self.connect_to_ibkr()
                if connected:
                    await self.execute_queued_trades()
            else:
                await self.execute_queued_trades()

        # 记录决策因素
        all_decisions = sell_decisions + buy_decisions
        if all_decisions:
            decision_factors.extend(all_decisions)

        logger.info(f"Chris 完成分析: {trades_executed} 笔交易")

        return {
            "trades_executed": trades_executed,
            "current_prices": {stock["ticker"]: stock["current_price"] for stock in target_stocks},
            "strategy": "精选蓝筹股长期持有",
            "decision_factors": "; ".join(decision_factors) if decision_factors else "未发现目标股票",
            "target_stocks": [stock["ticker"] for stock in target_stocks],
            "portfolio_allocation": "平均分配于5只蓝筹股",
            "target_universe": self.buy_only
        } if target_stocks or trades_executed > 0 else {}

    def trade(self, info: dict):
        ## 买入策略
        buy_only = [ticker for ticker in self.buy_only if ticker in info]
        for ticker in buy_only:
            if ticker in info:
                count = np.maximum(1, len(buy_only) - len(self.portfolio))
                units = int(self.uninvested / count // info[ticker]["price"])
                if units >= 1:
                    self.transact_capital(
                        ticker, units, info[ticker]["price"], type="buy"
                    )


class Dany(Bot):
    def __init__(
        self,
        capital: float,
        portfolio: dict = None,
        real_trading: bool = False,
        ibkr_config=None,
    ):
        super().__init__(capital, portfolio, real_trading, ibkr_config)
        self.min_rel_profit = 0.1
        self.max_rel_loss = 0.2

    async def analyze_and_trade(self, trading_signals: dict) -> dict:
        """
        Dany - 平衡价值投资者
        结合价值和趋势，风险容忍度适中
        """
        logger.info("Dany (平衡价值投资者) 分析市场信号...")
        
        # Dany的策略：平衡考虑低估股票和适度趋势
        target_stocks = []
        decision_factors = []

        # 第一优先级：极度低估股票
        extreme_below = trading_signals.get("extreme_below_trend", [])
        if extreme_below:
            top_extreme = sorted(extreme_below, key=lambda x: x["score"])[:2]
            target_stocks.extend(top_extreme)
            decision_factors.append(f"极度低估机会: {len(top_extreme)}只")

        # 第二优先级：高度低估股票
        highly_below = trading_signals.get("highly_below_trend", [])
        if highly_below and len(target_stocks) < 4:
            remaining_slots = 4 - len(target_stocks)
            top_highly = sorted(highly_below, key=lambda x: x["score"])[:remaining_slots]
            target_stocks.extend(top_highly)
            decision_factors.append(f"高度低估机会: {len(top_highly)}只")

        # 第三优先级：一般低估股票（如果前两类不足）
        below_trend = trading_signals.get("below_trend", [])
        if below_trend and len(target_stocks) < 6:
            remaining_slots = 6 - len(target_stocks)
            top_below = sorted(below_trend, key=lambda x: x["score"])[:remaining_slots]
            target_stocks.extend(top_below)
            decision_factors.append(f"适度低估机会: {len(top_below)}只")

        # 执行卖出策略（平衡的止盈止损）
        trades_executed = 0
        sell_decisions = []
        
        owned_stocks = list(self.portfolio.keys())
        for ticker in owned_stocks:
            current_price = None
            for category_stocks in trading_signals.values():
                if isinstance(category_stocks, list):
                    for stock in category_stocks:
                        if stock["ticker"] == ticker:
                            current_price = stock["current_price"]
                            break
                if current_price:
                    break
            
            if current_price:
                purchase_price = self.portfolio[ticker]["purchase_price"]
                rel_margin = (current_price - purchase_price) / purchase_price
                
                if rel_margin > self.min_rel_profit:
                    self.transact_capital(ticker, self.portfolio[ticker]["units"], current_price, type="sell")
                    sell_decisions.append(f"{ticker}: 止盈 (+{rel_margin:.1%})")
                    trades_executed += 1
                elif rel_margin < -self.max_rel_loss:
                    self.transact_capital(ticker, self.portfolio[ticker]["units"], current_price, type="sell")
                    sell_decisions.append(f"{ticker}: 止损 ({rel_margin:.1%})")
                    trades_executed += 1

        # 执行买入策略（适度分散）
        buy_decisions = []
        for stock in target_stocks:
            if stock["ticker"] not in self.portfolio:
                # Dany平衡策略：每只股票投入总资本的1/25，适度分散
                position_size = min(self.uninvested, self.capital / 25)
                units = int(position_size // stock["current_price"])
                
                if units >= 1:
                    self.transact_capital(stock["ticker"], units, stock["current_price"], type="buy")
                    buy_decisions.append(f"{stock['ticker']}: 平衡投资 (评分: {stock['score']:.2f})")
                    trades_executed += 1

        # 执行队列中的真实交易
        if hasattr(self, "execute_queued_trades"):
            if hasattr(self, "connect_to_ibkr"):
                connected = await self.connect_to_ibkr()
                if connected:
                    await self.execute_queued_trades()
            else:
                await self.execute_queued_trades()

        # 记录决策因素
        all_decisions = sell_decisions + buy_decisions
        if all_decisions:
            decision_factors.extend(all_decisions)

        logger.info(f"Dany 完成分析: {trades_executed} 笔交易")

        return {
            "trades_executed": trades_executed,
            "current_prices": {stock["ticker"]: stock["current_price"] for stock in target_stocks},
            "strategy": "平衡价值投资",
            "decision_factors": "; ".join(decision_factors) if decision_factors else "未发现合适机会",
            "target_stocks": [stock["ticker"] for stock in target_stocks],
            "portfolio_allocation": "1/25 适度分散投资"
        } if target_stocks or trades_executed > 0 else {}

    def trade(self, info: dict):
        ## 卖出策略
        owned_stocks = list(self.portfolio.keys())
        for ticker in owned_stocks:
            if ticker in info:  # 检查股票数据是否可用
                purchase_price = self.portfolio[ticker]["purchase_price"]
                rel_margin = (info[ticker]["price"] - purchase_price) / purchase_price
                if rel_margin > self.min_rel_profit or rel_margin < -self.max_rel_loss:
                    self.transact_capital(
                        ticker,
                        self.portfolio[ticker]["units"],
                        info[ticker]["price"],
                        type="sell",
                    )
            else:
                logger.warning(f"No price data available for owned stock {ticker}")

        ## 买入策略
        for ticker in info:
            if ticker not in self.portfolio.keys() and info[ticker]["rate"] in [
                "HIGHLY BELOW TREND",
                "BELOW TREND",
            ]:
                units = int(
                    np.minimum(self.uninvested, self.capital / 30)
                    // info[ticker]["price"]
                )
                if units >= 1:
                    self.transact_capital(
                        ticker, units, info[ticker]["price"], type="buy"
                    )


class Eddy(Bot):
    def __init__(
        self,
        capital: float,
        portfolio: dict = None,
        real_trading: bool = False,
        ibkr_config=None,
    ):
        super().__init__(capital, portfolio, real_trading, ibkr_config)
        self.min_rel_profit = 0.2
        self.max_rel_loss = 0.1

    async def analyze_and_trade(self, trading_signals: dict) -> dict:
        """
        Eddy - 激进趋势投资者
        专注于强势股票，追求更高收益
        """
        logger.info("Eddy (激进趋势投资者) 分析市场信号...")
        
        # Eddy的策略：专注于上涨趋势强劲的股票
        target_stocks = []
        decision_factors = []

        # 第一优先级：极度强势股票
        extreme_above = trading_signals.get("extreme_above_trend", [])
        if extreme_above:
            # 选择评分最高的前2只（最强势）
            top_extreme = sorted(extreme_above, key=lambda x: x["score"], reverse=True)[:2]
            target_stocks.extend(top_extreme)
            decision_factors.append(f"极度强势机会: {len(top_extreme)}只")
            logger.info(f"🚀 发现 {len(top_extreme)} 只极度强势股票")

        # 第二优先级：高度强势股票
        highly_above = trading_signals.get("highly_above_trend", [])
        if highly_above and len(target_stocks) < 4:
            remaining_slots = 4 - len(target_stocks)
            top_highly = sorted(highly_above, key=lambda x: x["score"], reverse=True)[:remaining_slots]
            target_stocks.extend(top_highly)
            decision_factors.append(f"高度强势机会: {len(top_highly)}只")

        # 第三优先级：普通强势股票
        above_trend = trading_signals.get("above_trend", [])
        if above_trend and len(target_stocks) < 6:
            remaining_slots = 6 - len(target_stocks)
            top_above = sorted(above_trend, key=lambda x: x["score"], reverse=True)[:remaining_slots]
            target_stocks.extend(top_above)
            decision_factors.append(f"普通强势机会: {len(top_above)}只")

        # 执行卖出策略（高期望止盈，快速止损）
        trades_executed = 0
        sell_decisions = []
        
        owned_stocks = list(self.portfolio.keys())
        for ticker in owned_stocks:
            current_price = None
            for category_stocks in trading_signals.values():
                if isinstance(category_stocks, list):
                    for stock in category_stocks:
                        if stock["ticker"] == ticker:
                            current_price = stock["current_price"]
                            break
                if current_price:
                    break
            
            if current_price:
                purchase_price = self.portfolio[ticker]["purchase_price"]
                rel_margin = (current_price - purchase_price) / purchase_price
                
                if rel_margin > self.min_rel_profit:
                    self.transact_capital(ticker, self.portfolio[ticker]["units"], current_price, type="sell")
                    sell_decisions.append(f"{ticker}: 高收益止盈 (+{rel_margin:.1%})")
                    trades_executed += 1
                elif rel_margin < -self.max_rel_loss:
                    self.transact_capital(ticker, self.portfolio[ticker]["units"], current_price, type="sell")
                    sell_decisions.append(f"{ticker}: 快速止损 ({rel_margin:.1%})")
                    trades_executed += 1

        # 执行买入策略（集中投资最强势股票）
        buy_decisions = []
        for stock in target_stocks:
            if stock["ticker"] not in self.portfolio:
                # Eddy激进策略：每只股票投入总资本的1/15，高度集中
                position_size = min(self.uninvested, self.capital / 15)
                units = int(position_size // stock["current_price"])
                
                if units >= 1:
                    self.transact_capital(stock["ticker"], units, stock["current_price"], type="buy")
                    buy_decisions.append(f"{stock['ticker']}: 激进趋势投资 (评分: {stock['score']:.2f})")
                    trades_executed += 1

        # 执行队列中的真实交易
        if hasattr(self, "execute_queued_trades"):
            if hasattr(self, "connect_to_ibkr"):
                connected = await self.connect_to_ibkr()
                if connected:
                    await self.execute_queued_trades()
            else:
                await self.execute_queued_trades()

        # 记录决策因素
        all_decisions = sell_decisions + buy_decisions
        if all_decisions:
            decision_factors.extend(all_decisions)

        logger.info(f"Eddy 完成分析: {trades_executed} 笔交易")

        return {
            "trades_executed": trades_executed,
            "current_prices": {stock["ticker"]: stock["current_price"] for stock in target_stocks},
            "strategy": "激进趋势投资",
            "decision_factors": "; ".join(decision_factors) if decision_factors else "未发现合适机会",
            "target_stocks": [stock["ticker"] for stock in target_stocks],
            "portfolio_allocation": "1/15 高度集中投资"
        } if target_stocks or trades_executed > 0 else {}

    def trade(self, info: dict):
        ## 卖出策略
        owned_stocks = list(self.portfolio.keys())
        for ticker in owned_stocks:
            if ticker in info:  # 检查股票数据是否可用
                purchase_price = self.portfolio[ticker]["purchase_price"]
                rel_margin = (info[ticker]["price"] - purchase_price) / purchase_price
                if rel_margin > self.min_rel_profit or rel_margin < -self.max_rel_loss:
                    self.transact_capital(
                        ticker,
                        self.portfolio[ticker]["units"],
                        info[ticker]["price"],
                        type="sell",
                    )
            else:
                logger.warning(f"No price data available for owned stock {ticker}")

        ## 买入策略
        for ticker in info:
            if ticker not in self.portfolio.keys() and info[ticker]["rate"] in [
                "HIGHLY ABOVE TREND",
                "ABOVE TREND",
            ]:
                units = int(
                    np.minimum(self.uninvested, self.capital / 30)
                    // info[ticker]["price"]
                )
                if units >= 1:
                    self.transact_capital(
                        ticker, units, info[ticker]["price"], type="buy"
                    )


class Flora(Bot):
    def __init__(
        self,
        capital: float,
        portfolio: dict = None,
        real_trading: bool = False,
        ibkr_config=None,
    ):
        super().__init__(capital, portfolio, real_trading, ibkr_config)
        self.min_rel_profit = 0.1
        self.max_rel_loss = 0.2

    async def analyze_and_trade(self, trading_signals: dict) -> dict:
        """
        Flora - 成长股投资者
        专注于稳定趋势中的高成长股票
        """
        logger.info("Flora (成长股投资者) 分析市场信号...")
        
        # Flora的策略：专注于趋势稳定且成长性强的股票
        target_stocks = []
        decision_factors = []

        # 主要关注沿着趋势的股票，但要求高成长性
        along_trend_stocks = trading_signals.get("along_trend", [])
        
        if along_trend_stocks:
            # 筛选高成长股票（预期增长≥100%）
            high_growth_stocks = []
            for stock in along_trend_stocks:
                growth_rate = (stock["predicted_price"] - stock["current_price"]) / stock["current_price"]
                if growth_rate >= 1.0:  # 100%成长预期
                    stock["calculated_growth"] = growth_rate
                    high_growth_stocks.append(stock)
            
            if high_growth_stocks:
                # 按成长性排序，选择前8只
                top_growth = sorted(high_growth_stocks, key=lambda x: x["calculated_growth"], reverse=True)[:8]
                target_stocks.extend(top_growth)
                decision_factors.append(f"高成长稳定股: {len(top_growth)}只")
                logger.info(f"🌱 发现 {len(top_growth)} 只高成长稳定股票")

        # 也考虑一些低估但成长性好的股票
        highly_below = trading_signals.get("highly_below_trend", [])
        if highly_below and len(target_stocks) < 6:
            growth_below_stocks = []
            for stock in highly_below:
                growth_rate = (stock["predicted_price"] - stock["current_price"]) / stock["current_price"]
                if growth_rate >= 0.5:  # 50%成长预期
                    stock["calculated_growth"] = growth_rate
                    growth_below_stocks.append(stock)
            
            if growth_below_stocks:
                remaining_slots = 6 - len(target_stocks)
                top_value_growth = sorted(growth_below_stocks, key=lambda x: x["calculated_growth"], reverse=True)[:remaining_slots]
                target_stocks.extend(top_value_growth)
                decision_factors.append(f"价值成长股: {len(top_value_growth)}只")

        # 执行卖出策略（适度止盈止损）
        trades_executed = 0
        sell_decisions = []
        
        owned_stocks = list(self.portfolio.keys())
        for ticker in owned_stocks:
            current_price = None
            for category_stocks in trading_signals.values():
                if isinstance(category_stocks, list):
                    for stock in category_stocks:
                        if stock["ticker"] == ticker:
                            current_price = stock["current_price"]
                            break
                if current_price:
                    break
            
            if current_price:
                purchase_price = self.portfolio[ticker]["purchase_price"]
                rel_margin = (current_price - purchase_price) / purchase_price
                
                if rel_margin > self.min_rel_profit:
                    self.transact_capital(ticker, self.portfolio[ticker]["units"], current_price, type="sell")
                    sell_decisions.append(f"{ticker}: 成长股止盈 (+{rel_margin:.1%})")
                    trades_executed += 1
                elif rel_margin < -self.max_rel_loss:
                    self.transact_capital(ticker, self.portfolio[ticker]["units"], current_price, type="sell")
                    sell_decisions.append(f"{ticker}: 止损 ({rel_margin:.1%})")
                    trades_executed += 1

        # 执行买入策略（按成长性分配资金）
        buy_decisions = []
        if target_stocks:
            # 按成长性重新排序，给予成长性高的股票更多资金
            target_stocks.sort(key=lambda x: x.get("calculated_growth", 0), reverse=True)
            
            total_growth = sum(stock.get("calculated_growth", 0) for stock in target_stocks)
            
            for i, stock in enumerate(target_stocks):
                if stock["ticker"] not in self.portfolio:
                    # Flora成长策略：根据成长性加权分配资金
                    growth_weight = stock.get("calculated_growth", 0) / total_growth if total_growth > 0 else 1/len(target_stocks)
                    position_size = min(self.uninvested * growth_weight, self.capital / 20)
                    units = int(position_size // stock["current_price"])
                    
                    if units >= 1:
                        self.transact_capital(stock["ticker"], units, stock["current_price"], type="buy")
                        buy_decisions.append(f"{stock['ticker']}: 成长投资 (成长率: {stock.get('calculated_growth', 0):.1%})")
                        trades_executed += 1

        # 执行队列中的真实交易
        if hasattr(self, "execute_queued_trades"):
            if hasattr(self, "connect_to_ibkr"):
                connected = await self.connect_to_ibkr()
                if connected:
                    await self.execute_queued_trades()
            else:
                await self.execute_queued_trades()

        # 记录决策因素
        all_decisions = sell_decisions + buy_decisions
        if all_decisions:
            decision_factors.extend(all_decisions)

        logger.info(f"Flora 完成分析: {trades_executed} 笔交易")

        return {
            "trades_executed": trades_executed,
            "current_prices": {stock["ticker"]: stock["current_price"] for stock in target_stocks},
            "strategy": "成长股投资",
            "decision_factors": "; ".join(decision_factors) if decision_factors else "未发现合适机会",
            "target_stocks": [stock["ticker"] for stock in target_stocks],
            "portfolio_allocation": "按成长性加权分配",
            "growth_focus": "≥100%成长预期"
        } if target_stocks or trades_executed > 0 else {}

    def trade(self, info: dict):
        ## 卖出策略
        owned_stocks = list(self.portfolio.keys())
        for ticker in owned_stocks:
            if ticker in info:  # 检查股票数据是否可用
                purchase_price = self.portfolio[ticker]["purchase_price"]
                rel_margin = (info[ticker]["price"] - purchase_price) / purchase_price
                if rel_margin > self.min_rel_profit or rel_margin < -self.max_rel_loss:
                    self.transact_capital(
                        ticker,
                        self.portfolio[ticker]["units"],
                        info[ticker]["price"],
                        type="sell",
                    )
            else:
                logger.warning(f"No price data available for owned stock {ticker}")

        ## 买入策略
        growths = np.array([info[ticker]["growth"] for ticker in info])
        idx = np.argsort(growths)[::-1]
        sorted_tickers = np.array(list(info.keys()))[idx]
        for ticker in sorted_tickers:
            if (
                ticker not in self.portfolio.keys()
                and info[ticker]["rate"] == "ALONG TREND"
                and info[ticker]["growth"] >= 1
            ):
                units = int(
                    np.minimum(self.uninvested, self.capital / 30)
                    // info[ticker]["price"]
                )
                if units >= 1:
                    self.transact_capital(
                        ticker, units, info[ticker]["price"], type="buy"
                    )
