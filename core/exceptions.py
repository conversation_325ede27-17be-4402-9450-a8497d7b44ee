#!/usr/bin/env python
"""
统一异常处理模块

定义系统中所有自定义异常类型和错误处理策略，
提供一致的错误处理接口和日志记录机制。
"""

import logging
import traceback
from enum import Enum
from typing import Any, Dict, Optional, Union
from functools import wraps


# ============================================================================
# 错误严重程度枚举 (Error Severity Enum)
# ============================================================================

class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"           # 低级错误，不影响主要功能
    MEDIUM = "medium"     # 中级错误，影响部分功能
    HIGH = "high"         # 高级错误，影响核心功能
    CRITICAL = "critical" # 严重错误，系统无法继续运行


class ErrorCategory(Enum):
    """错误类别"""
    CONNECTION = "connection"     # 连接相关错误
    DATA = "data"                # 数据相关错误
    TRADING = "trading"          # 交易相关错误
    VALIDATION = "validation"    # 验证相关错误
    CONFIGURATION = "config"     # 配置相关错误
    SYSTEM = "system"           # 系统相关错误


# ============================================================================
# 基础异常类 (Base Exception Classes)
# ============================================================================

class TradingSystemError(Exception):
    """交易系统基础异常类"""
    
    def __init__(
        self, 
        message: str, 
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        category: ErrorCategory = ErrorCategory.SYSTEM,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.severity = severity
        self.category = category
        self.error_code = error_code
        self.details = details or {}
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "message": self.message,
            "severity": self.severity.value,
            "category": self.category.value,
            "error_code": self.error_code,
            "details": self.details
        }


# ============================================================================
# 具体异常类 (Specific Exception Classes)
# ============================================================================

class IBKRConnectionError(TradingSystemError):
    """IBKR连接异常"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, **kwargs):
        super().__init__(
            message, 
            severity=ErrorSeverity.HIGH,
            category=ErrorCategory.CONNECTION,
            error_code=error_code,
            **kwargs
        )


class DataValidationError(TradingSystemError):
    """数据验证异常"""
    
    def __init__(self, message: str, invalid_data: Optional[Any] = None, **kwargs):
        details = kwargs.get('details', {})
        if invalid_data is not None:
            details['invalid_data'] = str(invalid_data)
        
        super().__init__(
            message,
            severity=ErrorSeverity.MEDIUM,
            category=ErrorCategory.VALIDATION,
            details=details,
            **kwargs
        )


class TradingExecutionError(TradingSystemError):
    """交易执行异常"""
    
    def __init__(self, message: str, ticker: Optional[str] = None, **kwargs):
        details = kwargs.get('details', {})
        if ticker:
            details['ticker'] = ticker
            
        super().__init__(
            message,
            severity=ErrorSeverity.HIGH,
            category=ErrorCategory.TRADING,
            details=details,
            **kwargs
        )


class ConfigurationError(TradingSystemError):
    """配置异常"""
    
    def __init__(self, message: str, config_key: Optional[str] = None, **kwargs):
        details = kwargs.get('details', {})
        if config_key:
            details['config_key'] = config_key
            
        super().__init__(
            message,
            severity=ErrorSeverity.CRITICAL,
            category=ErrorCategory.CONFIGURATION,
            details=details,
            **kwargs
        )


class DataRetrievalError(TradingSystemError):
    """数据获取异常"""
    
    def __init__(self, message: str, source: Optional[str] = None, **kwargs):
        details = kwargs.get('details', {})
        if source:
            details['source'] = source
            
        super().__init__(
            message,
            severity=ErrorSeverity.MEDIUM,
            category=ErrorCategory.DATA,
            details=details,
            **kwargs
        )


# ============================================================================
# 错误处理结果类 (Error Handling Result Classes)
# ============================================================================

class OperationResult:
    """操作结果类 - 统一的成功/失败返回格式"""
    
    def __init__(
        self, 
        success: bool, 
        data: Any = None, 
        error: Optional[TradingSystemError] = None,
        message: Optional[str] = None
    ):
        self.success = success
        self.data = data
        self.error = error
        self.message = message
        
    @classmethod
    def success_result(cls, data: Any = None, message: Optional[str] = None):
        """创建成功结果"""
        return cls(success=True, data=data, message=message)
    
    @classmethod
    def error_result(cls, error: TradingSystemError, message: Optional[str] = None):
        """创建错误结果"""
        return cls(success=False, error=error, message=message)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "success": self.success,
            "data": self.data,
            "message": self.message
        }
        
        if self.error:
            result["error"] = self.error.to_dict()
            
        return result


# ============================================================================
# 错误处理装饰器 (Error Handling Decorators)
# ============================================================================

def handle_errors(
    default_return=None,
    log_errors: bool = True,
    reraise: bool = False,
    error_category: ErrorCategory = ErrorCategory.SYSTEM
):
    """
    统一错误处理装饰器
    
    参数:
        default_return: 发生错误时的默认返回值
        log_errors: 是否记录错误日志
        reraise: 是否重新抛出异常
        error_category: 错误类别
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except TradingSystemError as e:
                if log_errors:
                    logger = logging.getLogger(func.__module__)
                    # 避免与日志记录字段冲突，使用安全的extra字段
                    safe_extra = {f"error_{k}": v for k, v in e.to_dict().items() if k not in ['message', 'msg', 'args']}
                    logger.error(f"Error in {func.__name__}: {e.message}", extra=safe_extra)
                
                if reraise:
                    raise
                return default_return
            except Exception as e:
                # 将普通异常包装为TradingSystemError
                trading_error = TradingSystemError(
                    message=f"Unexpected error in {func.__name__}: {str(e)}",
                    category=error_category,
                    severity=ErrorSeverity.HIGH,
                    details={"traceback": traceback.format_exc()}
                )
                
                if log_errors:
                    logger = logging.getLogger(func.__module__)
                    # 避免与日志记录字段冲突，使用安全的extra字段
                    safe_extra = {f"error_{k}": v for k, v in trading_error.to_dict().items() if k not in ['message', 'msg', 'args']}
                    logger.error(f"Unexpected error in {func.__name__}: {str(e)}",
                               extra=safe_extra)
                
                if reraise:
                    raise trading_error
                return default_return
        return wrapper
    return decorator


def async_handle_errors(
    default_return=None,
    log_errors: bool = True,
    reraise: bool = False,
    error_category: ErrorCategory = ErrorCategory.SYSTEM
):
    """
    异步函数的统一错误处理装饰器
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except TradingSystemError as e:
                if log_errors:
                    logger = logging.getLogger(func.__module__)
                    # 避免与日志记录字段冲突，使用安全的extra字段
                    safe_extra = {f"error_{k}": v for k, v in e.to_dict().items() if k not in ['message', 'msg', 'args']}
                    logger.error(f"Error in {func.__name__}: {e.message}", extra=safe_extra)
                
                if reraise:
                    raise
                return default_return
            except Exception as e:
                # 将普通异常包装为TradingSystemError
                trading_error = TradingSystemError(
                    message=f"Unexpected error in {func.__name__}: {str(e)}",
                    category=error_category,
                    severity=ErrorSeverity.HIGH,
                    details={"traceback": traceback.format_exc()}
                )
                
                if log_errors:
                    logger = logging.getLogger(func.__module__)
                    # 避免与日志记录字段冲突，使用安全的extra字段
                    safe_extra = {f"error_{k}": v for k, v in trading_error.to_dict().items() if k not in ['message', 'msg', 'args']}
                    logger.error(f"Unexpected error in {func.__name__}: {str(e)}",
                               extra=safe_extra)
                
                if reraise:
                    raise trading_error
                return default_return
        return wrapper
    return decorator


# ============================================================================
# 错误处理工具函数 (Error Handling Utility Functions)
# ============================================================================

def safe_execute(
    func,
    *args,
    default_return=None,
    log_errors: bool = True,
    error_category: ErrorCategory = ErrorCategory.SYSTEM,
    **kwargs
) -> OperationResult:
    """
    安全执行函数，返回OperationResult

    参数:
        func: 要执行的函数
        *args: 函数参数
        default_return: 默认返回值
        log_errors: 是否记录错误
        error_category: 错误类别（不会传递给被调用的函数）
        **kwargs: 函数关键字参数

    返回:
        OperationResult: 操作结果
    """
    try:
        result = func(*args, **kwargs)
        return OperationResult.success_result(data=result)
    except TradingSystemError as e:
        if log_errors:
            logger = logging.getLogger(func.__module__)
            # 避免与日志记录字段冲突，使用安全的extra字段
            safe_extra = {f"error_{k}": v for k, v in e.to_dict().items() if k not in ['message', 'msg', 'args']}
            logger.error(f"Error in {func.__name__}: {e.message}", extra=safe_extra)
        return OperationResult.error_result(error=e)
    except Exception as e:
        trading_error = TradingSystemError(
            message=f"Unexpected error in {func.__name__}: {str(e)}",
            category=error_category,
            severity=ErrorSeverity.HIGH,
            details={"traceback": traceback.format_exc()}
        )
        
        if log_errors:
            logger = logging.getLogger(func.__module__)
            # 避免与日志记录字段冲突，使用安全的extra字段
            safe_extra = {f"error_{k}": v for k, v in trading_error.to_dict().items() if k not in ['message', 'msg', 'args']}
            logger.error(f"Unexpected error in {func.__name__}: {str(e)}",
                       extra=safe_extra)
        
        return OperationResult.error_result(error=trading_error)


def validate_and_raise(condition: bool, message: str, error_class=DataValidationError, **kwargs):
    """
    验证条件，如果失败则抛出指定异常
    
    参数:
        condition: 验证条件
        message: 错误消息
        error_class: 异常类
        **kwargs: 异常的额外参数
    """
    if not condition:
        raise error_class(message, **kwargs)


# ============================================================================
# 错误恢复策略 (Error Recovery Strategies)
# ============================================================================

class RetryStrategy:
    """重试策略"""
    
    def __init__(self, max_attempts: int = 3, delay: float = 1.0, backoff_factor: float = 2.0):
        self.max_attempts = max_attempts
        self.delay = delay
        self.backoff_factor = backoff_factor
    
    def execute(self, func, *args, **kwargs):
        """执行带重试的函数"""
        import time
        
        last_exception = None
        current_delay = self.delay
        
        for attempt in range(self.max_attempts):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                if attempt < self.max_attempts - 1:
                    time.sleep(current_delay)
                    current_delay *= self.backoff_factor
                else:
                    break
        
        # 如果所有重试都失败，抛出最后一个异常
        if isinstance(last_exception, TradingSystemError):
            raise last_exception
        else:
            raise TradingSystemError(
                message=f"Function failed after {self.max_attempts} attempts: {str(last_exception)}",
                severity=ErrorSeverity.HIGH,
                details={"original_error": str(last_exception)}
            )
