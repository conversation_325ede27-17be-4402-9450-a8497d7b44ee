#!/usr/bin/env python3
"""
期权交易参数配置模块

提供期权交易策略的参数配置和优化设置，包括风险管理参数、
交易策略参数、性能优化配置等。支持动态参数调整和配置摘要显示。
"""

from typing import Dict, Any
from config import DEFAULT_CONFIG, DEFAULT_OPTIONS_CONFIG


def get_optimization_summary() -> str:
    """获取期权优化配置摘要
    
    返回:
        str: 格式化的配置摘要字符串
    """
    config = DEFAULT_OPTIONS_CONFIG
    
    return f"""
🎯 期权交易优化配置
{"=" * 50}

📊 基础策略参数:
  • 最大期权仓位: {config.max_option_positions}
  • 单策略最大风险: {config.max_single_strategy_risk * 100:.1f}%
  • 最小相关性阈值: {config.min_correlation:.2f}
  • 背离检测阈值: {config.min_divergence:.2f}

⏰ 时间参数:
  • 最佳到期时间: {config.optimal_time_to_expiry}天
  • 时间衰减出场: {config.time_decay_exit_days}天
  • 周末出场阈值: {config.weekend_exit_threshold}天

💰 流动性要求:
  • 最小日成交量: {config.min_daily_volume:,}
  • 最小未平仓合约: {config.min_open_interest:,}
  • 最大买卖价差: {config.max_bid_ask_spread * 100:.1f}%

🎯 价格范围:
  • 最小期权价格: ${config.min_option_price:.2f}
  • 最佳看涨价值: {config.optimal_moneyness_call:.2f}
  • 最佳看跌价值: {config.optimal_moneyness_put:.2f}

⚡ 性能优化:
  • 数据缓存: {'启用' if config.enable_options_data_cache else '禁用'}
  • 缓存过期: {config.cache_expiry_minutes}分钟
  • 并发请求数: {config.max_concurrent_option_requests}
  • 并行评分: {'启用' if config.enable_parallel_scoring else '禁用'}
  • 批处理大小: {config.batch_size_option_analysis}

📈 监控设置:
  • 实时监控: {'启用' if config.enable_real_time_monitoring else '禁用'}
  • 监控间隔: {config.monitoring_interval_seconds}秒

🛡️ 风险管理:
  • 动态风险调整: {'启用' if config.enable_dynamic_risk else '禁用'}
  • 波动率风险调整: {'启用' if config.volatility_risk_adjustment else '禁用'}
  • 期权止损: {config.options_stop_loss * 100:.1f}%
  • 最大期权配置: {config.max_options_allocation * 100:.1f}%

🎯 评分权重:
  • 流动性权重: {config.scoring_weights['liquidity'] * 100:.0f}%
  • 背离权重: {config.scoring_weights['divergence'] * 100:.0f}%
  • 置信度权重: {config.scoring_weights['confidence'] * 100:.0f}%
  • 价差权重: {config.scoring_weights['spread'] * 100:.0f}%
  • 价值权重: {config.scoring_weights['moneyness'] * 100:.0f}%
  • 波动率权重: {config.scoring_weights['volatility'] * 100:.0f}%
  • 时间衰减权重: {config.scoring_weights['time_decay'] * 100:.0f}%

💡 优化建议:
  • 建议在市场开盘前30分钟启动系统进行数据预热
  • 建议在高波动期间降低仓位规模
  • 建议定期检查和调整相关性阈值
  • 建议在财报季节提高风险管理级别
{"=" * 50}
"""


def get_strategy_parameters() -> Dict[str, Any]:
    """获取策略参数字典

    返回:
        Dict[str, Any]: 策略参数字典
    """
    config = DEFAULT_OPTIONS_CONFIG

    return {
        "max_option_positions": config.max_option_positions,
        "max_single_strategy_risk": config.max_single_strategy_risk,
        "min_correlation": config.min_correlation,
        "min_divergence": config.min_divergence,
        "optimal_time_to_expiry": config.optimal_time_to_expiry,
        "time_decay_exit_days": config.time_decay_exit_days,
        "weekend_exit_threshold": config.weekend_exit_threshold,
        "min_daily_volume": config.min_daily_volume,
        "min_open_interest": config.min_open_interest,
        "max_bid_ask_spread": config.max_bid_ask_spread,
        "min_option_price": config.min_option_price,
        "optimal_moneyness_call": config.optimal_moneyness_call,
        "optimal_moneyness_put": config.optimal_moneyness_put,
        "options_stop_loss": config.options_stop_loss,
        "max_options_allocation": config.max_options_allocation,
        "scoring_weights": config.scoring_weights,
    }


def get_performance_parameters() -> Dict[str, Any]:
    """获取性能优化参数字典
    
    返回:
        Dict[str, Any]: 性能参数字典
    """
    config = DEFAULT_OPTIONS_CONFIG
    
    return {
        "enable_options_data_cache": config.enable_options_data_cache,
        "cache_expiry_minutes": config.cache_expiry_minutes,
        "max_concurrent_option_requests": config.max_concurrent_option_requests,
        "enable_parallel_scoring": config.enable_parallel_scoring,
        "batch_size_option_analysis": config.batch_size_option_analysis,
        "enable_real_time_monitoring": config.enable_real_time_monitoring,
        "monitoring_interval_seconds": config.monitoring_interval_seconds,
    }


def get_risk_parameters() -> Dict[str, Any]:
    """获取风险管理参数字典

    返回:
        Dict[str, Any]: 风险管理参数字典
    """
    config = DEFAULT_OPTIONS_CONFIG

    return {
        "enable_dynamic_risk": config.enable_dynamic_risk,
        "volatility_risk_adjustment": config.volatility_risk_adjustment,
        "volatility_adjustment_factors": config.volatility_adjustment_factors,
        "options_stop_loss": config.options_stop_loss,
        "max_options_allocation": config.max_options_allocation,
        "max_single_strategy_risk": config.max_single_strategy_risk,
    }


def validate_parameters() -> bool:
    """验证参数配置的有效性

    返回:
        bool: 参数配置是否有效
    """
    config = DEFAULT_OPTIONS_CONFIG

    # 基本参数验证
    if config.max_option_positions <= 0:
        return False

    if not (0 < config.max_single_strategy_risk <= 1):
        return False

    if not (0 <= config.min_correlation <= 1):
        return False

    if config.time_decay_exit_days <= 0:
        return False

    if config.min_option_price <= 0:
        return False

    # 权重验证
    total_weight = sum(config.scoring_weights.values())
    if abs(total_weight - 1.0) > 0.01:  # 允许1%的误差
        return False

    return True


if __name__ == "__main__":
    # 测试配置显示
    print(get_optimization_summary())
    
    # 验证参数
    if validate_parameters():
        print("\n✅ 参数配置验证通过")
    else:
        print("\n❌ 参数配置验证失败")
