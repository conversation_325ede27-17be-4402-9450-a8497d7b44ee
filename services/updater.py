#!/usr/bin/env python
"""股票池更新器

全面的股票和分类管理系统，集成股票列表更新和股票分类信息管理。
从多个数据源获取最新的股票、行业分类和市值信息，维护系统股票池的准确性。
支持IBKR API集成和外部数据源同步。
"""

import asyncio
import csv
import logging
import os
import time
from datetime import datetime
from typing import Dict, Optional, Set

import pandas as pd
import requests

from core.broker import get_ibkr_client
from config import get_config
from config import IBKRConfig
from core.exceptions import (
    ErrorCategory,
    handle_errors,
    async_handle_errors,
    safe_execute
)

# 使用统一配置管理日志
logger = logging.getLogger(__name__)


class StockUniverseUpdater:
    """
    全面的股票池管理系统
    - 从多个来源获取股票
    - 使用 IBKR 验证股票
    - 更新 symbols_list.txt 和 stock_info.csv
    - 维护板块和行业分类
    """

    def __init__(self, config: Optional[IBKRConfig] = None):
        # 使用统一配置
        unified_config = get_config()
        self.config = config or unified_config.ibkr
        self.system_config = unified_config.system
        self.ibkr_client = get_ibkr_client("updater", self.config)

        # 使用配置中的文件路径
        self.stock_info_file = self.system_config.stock_info_file
        self.symbols_file = self.system_config.symbols_file
        self.backup_dir = os.path.join(self.system_config.data_dir, "stock_info/backups")

        # 创建必要目录
        self._ensure_directories()

    @handle_errors(reraise=True, error_category=ErrorCategory.SYSTEM)
    def _ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            os.path.dirname(self.stock_info_file),
            os.path.dirname(self.symbols_file),
            self.backup_dir
        ]

        for directory in directories:
            if directory:  # 避免空路径
                os.makedirs(directory, exist_ok=True)
                logger.debug(f"确保目录存在: {directory}")

    @handle_errors(reraise=True, error_category=ErrorCategory.SYSTEM)
    def backup_existing_files(self):
        """创建现有文件的备份"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        for filename in [self.stock_info_file, self.symbols_file]:
            if os.path.exists(filename):
                # Extract just the filename without path for backup
                base_filename = os.path.basename(filename)
                backup_path = os.path.join(
                    self.backup_dir, f"{base_filename}.backup_{timestamp}"
                )
                # 使用安全的文件操作而不是os.system
                import shutil
                shutil.copy2(filename, backup_path)
                logger.info(f"已备份 {filename} 到 {backup_path}")

    @handle_errors(default_return=set(), error_category=ErrorCategory.DATA)
    def get_comprehensive_symbol_sources(self) -> Set[str]:
        """从多个综合源获取股票"""
        all_symbols = set()

        # Source 1: REMOVED - No more old GitHub files with outdated symbols
        # We now use only real-time, current data sources
        logger.info("🚫 跳过旧的GitHub文件 - 仅使用当前数据源")

        # Source 1: S&P 500 companies (current constituents)
        sp500_result = safe_execute(
            self._get_sp500_symbols,
            log_errors=True
        )
        if sp500_result.success and sp500_result.data:
            all_symbols.update(sp500_result.data)
            logger.info(f"✅ S&P 500: {len(sp500_result.data)}个当前股票")
        else:
            logger.warning("S&P 500数据获取失败")

        # Source 2: NASDAQ API (real-time data)
        nasdaq_result = safe_execute(
            self._get_nasdaq_symbols,
            log_errors=True
        )
        if nasdaq_result.success and nasdaq_result.data:
            all_symbols.update(nasdaq_result.data)
            logger.info(f"✅ NASDAQ API: {len(nasdaq_result.data)}个实时股票")
        else:
            logger.warning("NASDAQ API获取失败")

        # Source 3: Essential symbols
        essential_result = safe_execute(
            self._get_essential_symbols,
            log_errors=True
        )
        if essential_result.success and essential_result.data:
            all_symbols.update(essential_result.data)
            logger.info(f"✅ 核心股票: {len(essential_result.data)}个已验证股票")

        return all_symbols

    def _get_sp500_symbols(self) -> Set[str]:
        """获取S&P 500成分股"""
        logger.info("🔄 获取当前S&P 500成分股...")
        sp500_url = "https://raw.githubusercontent.com/datasets/s-and-p-500-companies/master/data/constituents.csv"
        response = requests.get(sp500_url, timeout=self.system_config.request_timeout)
        response.raise_for_status()

        import io
        df = pd.read_csv(io.StringIO(response.text))
        return set(df["Symbol"].tolist())

    def _get_nasdaq_symbols(self) -> Set[str]:
        """从NASDAQ API获取实时股票"""
        logger.info("🔄 从NASDAQ API获取实时股票...")
        nasdaq_url = "https://api.nasdaq.com/api/screener/stocks?tableonly=true&limit=25000&download=true"
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        response = requests.get(nasdaq_url, headers=headers, timeout=self.system_config.request_timeout)
        response.raise_for_status()

        data = response.json()
        nasdaq_symbols = set()

        if "data" in data and "rows" in data["data"]:
            for row in data["data"]["rows"]:
                if row.get("symbol") and len(row["symbol"]) <= 6:
                    symbol = row["symbol"].strip().upper()
                    # Filter for NYSE and NASDAQ only
                    if row.get("exchange"):
                        exchange = row["exchange"].upper()
                        if exchange in ["NASDAQ", "NYSE"]:
                            nasdaq_symbols.add(symbol)
                    else:
                        nasdaq_symbols.add(symbol)  # Include if exchange not specified

        return nasdaq_symbols

    def _get_essential_symbols(self) -> Set[str]:
        """获取核心必需股票"""
        logger.info("🔄 添加已验证的当前主要股票...")
        return {
                # Technology - Current symbols (verified 2024)
                "AAPL",  # Apple Inc.
                "MSFT",  # Microsoft Corporation
                "GOOGL",  # Alphabet Inc. Class A (current)
                "GOOG",  # Alphabet Inc. Class C (current)
                "AMZN",  # Amazon.com Inc.
                "META",  # Meta Platforms Inc. (formerly FB)
                "TSLA",  # Tesla Inc.
                "NVDA",  # NVIDIA Corporation
                "NFLX",  # Netflix Inc.
                "ADBE",  # Adobe Inc.
                "CRM",  # Salesforce Inc.
                "ORCL",  # Oracle Corporation
                "INTC",  # Intel Corporation
                "AMD",  # Advanced Micro Devices
                "QCOM",  # QUALCOMM Incorporated
                "AVGO",  # Broadcom Inc.
                "CSCO",  # Cisco Systems Inc.
                "TXN",  # Texas Instruments
                # Finance - Current symbols
                "JPM",  # JPMorgan Chase & Co.
                "BAC",  # Bank of America Corporation
                "WFC",  # Wells Fargo & Company
                "C",  # Citigroup Inc.
                "GS",  # Goldman Sachs Group Inc.
                "MS",  # Morgan Stanley
                "V",  # Visa Inc.
                "MA",  # Mastercard Incorporated
                "PYPL",  # PayPal Holdings Inc.
                "AXP",  # American Express Company
                "BLK",  # BlackRock Inc.
                "SCHW",  # Charles Schwab Corporation
                # Healthcare - Current symbols
                "JNJ",  # Johnson & Johnson
                "UNH",  # UnitedHealth Group Incorporated
                "PFE",  # Pfizer Inc.
                "ABBV",  # AbbVie Inc.
                "MRK",  # Merck & Co. Inc.
                "TMO",  # Thermo Fisher Scientific Inc.
                "ABT",  # Abbott Laboratories
                "DHR",  # Danaher Corporation
                "BMY",  # Bristol-Myers Squibb Company
                "AMGN",  # Amgen Inc.
                "GILD",  # Gilead Sciences Inc.
                "REGN",  # Regeneron Pharmaceuticals
                "VRTX",  # Vertex Pharmaceuticals
                "ISRG",  # Intuitive Surgical Inc.
                # Consumer - Current symbols
                "WMT",  # Walmart Inc.
                "HD",  # Home Depot Inc.
                "PG",  # Procter & Gamble Company
                "KO",  # Coca-Cola Company
                "PEP",  # PepsiCo Inc.
                "COST",  # Costco Wholesale Corporation
                "MCD",  # McDonald's Corporation
                "NKE",  # NIKE Inc.
                "SBUX",  # Starbucks Corporation
                "TGT",  # Target Corporation
                "LOW",  # Lowe's Companies Inc.
                "DIS",  # Walt Disney Company
                # Communication Services - Current symbols
                "T",  # AT&T Inc.
                "VZ",  # Verizon Communications Inc.
                "CMCSA",  # Comcast Corporation
                "CHTR",  # Charter Communications Inc.
                "TMUS",  # T-Mobile US Inc.
                # Industrial - Current symbols
                "BA",  # Boeing Company
                "CAT",  # Caterpillar Inc.
                "GE",  # General Electric Company
                "MMM",  # 3M Company
                "HON",  # Honeywell International Inc.
                "UPS",  # United Parcel Service Inc.
                "FDX",  # FedEx Corporation
                "LMT",  # Lockheed Martin Corporation
                "RTX",  # Raytheon Technologies Corporation
                "DE",  # Deere & Company
                # Energy - Current symbols
                "XOM",  # Exxon Mobil Corporation
                "CVX",  # Chevron Corporation
                "COP",  # ConocoPhillips
                "EOG",  # EOG Resources Inc.
                "SLB",  # Schlumberger Limited
                "PSX",  # Phillips 66
                "VLO",  # Valero Energy Corporation
                "MPC",  # Marathon Petroleum Corporation
                "OXY",  # Occidental Petroleum Corporation
                # Major ETFs (for reference)
                "SPY",  # SPDR S&P 500 ETF Trust
                "QQQ",  # Invesco QQQ Trust
                "IWM",  # iShares Russell 2000 ETF
                "VTI",  # Vanguard Total Stock Market ETF
                "VOO",  # Vanguard S&P 500 ETF
            }



    @async_handle_errors(default_return=set(), error_category=ErrorCategory.CONNECTION)
    async def get_all_stocks_from_ibkr_exchanges(self) -> Set[str]:
        """使用多种IBKR扫描器策略从NYSE和NASDAQ获取综合股票列表"""
        logger.info(
            "🔍 使用综合IBKR扫描从NYSE/NASDAQ获取所有股票..."
        )

        if not await self.ibkr_client.connect():
            logger.error("连接到IBKR进行综合股票扫描失败")
            return set()

        all_symbols = set()

        try:
            # Import ib_insync with error handling
            try:
                from ib_insync import ScannerSubscription
            except ImportError:
                logger.error("ib_insync不可用于扫描器功能")
                return set()

            # Simplified scanner configurations using only available scan codes
            # Focus on scan codes that work without special market data subscriptions
            scanner_configs = [
                # NYSE configurations - using only working scan codes
                {
                    "name": "NYSE Top Gainers",
                    "instrument": "STK",
                    "locationCode": "STK.NYSE",
                    "scanCode": "TOP_PERC_GAIN",
                },
                {
                    "name": "NYSE Top Losers",
                    "instrument": "STK",
                    "locationCode": "STK.NYSE",
                    "scanCode": "TOP_PERC_LOSE",
                },
                {
                    "name": "NYSE Most Active",
                    "instrument": "STK",
                    "locationCode": "STK.NYSE",
                    "scanCode": "MOST_ACTIVE",
                },
                {
                    "name": "NYSE Hot by Volume",
                    "instrument": "STK",
                    "locationCode": "STK.NYSE",
                    "scanCode": "HOT_BY_VOLUME",
                },
                # NASDAQ configurations - using only working scan codes
                {
                    "name": "NASDAQ Top Gainers",
                    "instrument": "STK",
                    "locationCode": "STK.NASDAQ",
                    "scanCode": "TOP_PERC_GAIN",
                },
                {
                    "name": "NASDAQ Top Losers",
                    "instrument": "STK",
                    "locationCode": "STK.NASDAQ",
                    "scanCode": "TOP_PERC_LOSE",
                },
                {
                    "name": "NASDAQ Most Active",
                    "instrument": "STK",
                    "locationCode": "STK.NASDAQ",
                    "scanCode": "MOST_ACTIVE",
                },
                {
                    "name": "NASDAQ Hot by Volume",
                    "instrument": "STK",
                    "locationCode": "STK.NASDAQ",
                    "scanCode": "HOT_BY_VOLUME",
                },
                # Additional working scan codes for broader coverage
                {
                    "name": "NYSE Top Open Gain",
                    "instrument": "STK",
                    "locationCode": "STK.NYSE",
                    "scanCode": "TOP_OPEN_PERC_GAIN",
                },
                {
                    "name": "NASDAQ Top Open Gain",
                    "instrument": "STK",
                    "locationCode": "STK.NASDAQ",
                    "scanCode": "TOP_OPEN_PERC_GAIN",
                },
                {
                    "name": "NYSE Top Open Loss",
                    "instrument": "STK",
                    "locationCode": "STK.NYSE",
                    "scanCode": "TOP_OPEN_PERC_LOSE",
                },
                {
                    "name": "NASDAQ Top Open Loss",
                    "instrument": "STK",
                    "locationCode": "STK.NASDAQ",
                    "scanCode": "TOP_OPEN_PERC_LOSE",
                },
            ]

            total_scans = len(scanner_configs)
            logger.info(
                f"📊 运行{total_scans}个简化扫描以捕获NYSE/NASDAQ股票..."
            )

            req_id = 1000  # Start with a high request ID to avoid conflicts

            for i, config in enumerate(scanner_configs, 1):
                try:
                    logger.info(f"📈 [{i}/{total_scans}] 扫描{config['name']}...")

                    # Create scanner subscription
                    scanner = ScannerSubscription(
                        instrument=config["instrument"],
                        locationCode=config["locationCode"],
                        scanCode=config["scanCode"],
                    )

                    # Request scanner data with explicit request ID
                    scanner_data = self.ibkr_client.ib.reqScannerData(scanner, [], [])

                    # Wait for data with shorter timeout
                    await asyncio.sleep(self.system_config.scanner_wait_time)

                    if scanner_data:
                        exchange_symbols = set()
                        for item in scanner_data:
                            if hasattr(item, "contract") and hasattr(
                                item.contract, "symbol"
                            ):
                                symbol = item.contract.symbol
                                # Validate symbol format
                                if (
                                    symbol
                                    and isinstance(symbol, str)
                                    and len(symbol.strip()) >= 1
                                    and len(symbol.strip()) <= 5
                                    and symbol.strip()
                                    .replace(".", "")
                                    .replace("-", "")
                                    .isalpha()
                                ):  # Allow dots and dashes
                                    clean_symbol = symbol.strip().upper()
                                    exchange_symbols.add(clean_symbol)

                        all_symbols.update(exchange_symbols)
                        logger.info(
                            f"   ✅ 从{config['name']}找到{len(exchange_symbols)}个股票 (总计: {len(all_symbols)})"
                        )
                    else:
                        logger.warning(f"   ⚠️ 从{config['name']}未返回数据")

                    # Cancel scanner subscription properly (需要传入 ScanDataList 对象)
                    try:
                        if scanner_data:
                            self.ibkr_client.ib.cancelScannerSubscription(scanner_data)
                    except Exception as cancel_error:
                        logger.debug(f"取消扫描器错误: {cancel_error}")

                    # Rate limiting between scanner requests
                    await asyncio.sleep(1.5)
                    req_id += 1

                except Exception as e:
                    logger.warning(f"扫描{config['name']}失败: {e}")
                    # Continue with next scanner even if this one fails
                    continue

        except Exception as e:
            logger.error(f"IBKR综合扫描器失败: {e}")
        finally:
            self.ibkr_client.disconnect()

        logger.info(
            f"🎯 IBKR综合扫描器从NYSE/NASDAQ找到{len(all_symbols)}个唯一股票"
        )
        return all_symbols

    async def get_symbols_from_ibkr_scanner(self) -> Set[str]:
        """使用IBKR市场扫描器获取综合股票列表 (旧方法)"""
        logger.info("🔍 从IBKR市场扫描器获取股票列表 (传统方法)...")

        if not await self.ibkr_client.connect():
            logger.error("连接到IBKR进行市场扫描失败")
            return set()

        all_symbols = set()

        try:
            # Import ib_insync with error handling
            try:
                from ib_insync import ScannerSubscription
            except ImportError:
                logger.error("ib_insync不可用于扫描器功能")
                return set()

            # Define scanner parameters for NYSE and NASDAQ only
            scanner_configs = [
                {
                    "name": "NASDAQ Stocks",
                    "instrument": "STK",
                    "locationCode": "STK.NASDAQ",
                    "scanCode": "TOP_PERC_GAIN",
                },
                {
                    "name": "NYSE Stocks",
                    "instrument": "STK",
                    "locationCode": "STK.NYSE",
                    "scanCode": "TOP_PERC_GAIN",
                },
                # Removed AMEX - only trading NYSE and NASDAQ stocks
            ]

            for config in scanner_configs:
                try:
                    logger.info(f"📊 扫描{config['name']}...")

                    # Create scanner subscription
                    scanner = ScannerSubscription(
                        instrument=config["instrument"],
                        locationCode=config["locationCode"],
                        scanCode=config["scanCode"],
                    )

                    # Request scanner data (get maximum results)
                    scanner_data = self.ibkr_client.ib.reqScannerData(scanner)

                    # Wait for data
                    await asyncio.sleep(self.system_config.scanner_wait_time)

                    if scanner_data:
                        exchange_symbols = set()
                        for item in scanner_data:
                            if hasattr(item, "contract") and hasattr(
                                item.contract, "symbol"
                            ):
                                symbol = item.contract.symbol
                                if symbol and len(symbol) <= 5 and symbol.isalpha():
                                    exchange_symbols.add(symbol)

                        all_symbols.update(exchange_symbols)
                        logger.info(
                            f"   从{config['name']}找到{len(exchange_symbols)}个股票"
                        )

                    # Cancel scanner to free resources (需要传入 ScanDataList 对象)
                    try:
                        if scanner_data:
                            self.ibkr_client.ib.cancelScannerSubscription(scanner_data)
                    except Exception as cancel_error:
                        logger.debug(f"取消扫描器错误: {cancel_error}")

                    # Rate limiting between scanner requests
                    await asyncio.sleep(self.system_config.rate_limit_delay)

                except Exception as e:
                    logger.warning(f"扫描{config['name']}失败: {e}")
                    continue

        except Exception as e:
            logger.error(f"IBKR扫描器失败: {e}")
        finally:
            self.ibkr_client.disconnect()

        logger.info(f"🎯 IBKR扫描器找到{len(all_symbols)}个总股票")
        return all_symbols

    @async_handle_errors(default_return=set(), error_category=ErrorCategory.DATA)
    async def get_enhanced_symbol_sources(self) -> Set[str]:
        """从Yahoo Finance获取NYSE和NASDAQ交易所的股票"""
        all_symbols = set()

        logger.info(
            "🚀 使用Yahoo Finance作为NYSE/NASDAQ股票的主要来源..."
        )
        logger.info("📈 从Yahoo Finance获取综合股票列表")

        # Get symbols from Yahoo Finance using unified error handling
        yahoo_result = safe_execute(
            self.get_yahoo_finance_symbols,
            log_errors=True
        )
        if yahoo_result.success and yahoo_result.data:
            all_symbols.update(yahoo_result.data)
            logger.info(
                f"✅ Yahoo Finance: 从 NYSE/NASDAQ 获取 {len(yahoo_result.data)} 个股票"
            )
        else:
            logger.warning(
                "Yahoo Finance returned no symbols, trying backup sources..."
            )

        # Backup: Use comprehensive symbol sources if Yahoo Finance fails
        if len(all_symbols) == 0:
            logger.info("🔄 使用备用综合来源...")
            backup_result = safe_execute(
                self.get_comprehensive_symbol_sources,
                log_errors=True
            )
            if backup_result.success and backup_result.data:
                all_symbols.update(backup_result.data)
                logger.info(f"✅ 备用来源: {len(backup_result.data)}个股票")
            else:
                logger.error("备用来源也失败了")

        logger.info(f"📊 来自所有来源的总股票: {len(all_symbols)}")

        if len(all_symbols) == 0:
            logger.error("❌ 严重: 未从任何来源获得股票!")
            logger.error("💡 请检查互联网连接和数据源")

        return all_symbols

    def get_yahoo_finance_symbols(self) -> Set[str]:
        """从Yahoo Finance和外部源获取NYSE和NASDAQ的综合股票"""
        all_symbols = set()

        logger.info("📈 获取NYSE和NASDAQ综合股票列表...")

        # Method 1: Use existing comprehensive symbol sources (which includes Yahoo Finance data)
        try:
            comprehensive_symbols = self.get_comprehensive_symbol_sources()
            if comprehensive_symbols:
                all_symbols.update(comprehensive_symbols)
                logger.info(
                    f"✅ 综合数据源: {len(comprehensive_symbols)} 个股票"
                )
        except Exception as e:
            logger.warning(f"综合来源失败: {e}")

        # Method 2: Add additional Yahoo Finance specific symbols
        try:
            logger.info("📊 添加Yahoo Finance特定股票...")

            # Get additional symbols using yfinance
            # Add major indices components
            major_symbols = {
                # S&P 500 major components
                "AAPL",
                "MSFT",
                "AMZN",
                "GOOGL",
                "GOOG",
                "TSLA",
                "META",
                "NVDA",
                "BRK-B",
                "UNH",
                "JNJ",
                "JPM",
                "V",
                "PG",
                "HD",
                "CVX",
                "MA",
                "ABBV",
                "PFE",
                "AVGO",
                "KO",
                "MRK",
                "COST",
                "PEP",
                "TMO",
                "WMT",
                "BAC",
                "ABT",
                "DIS",
                "ADBE",
                "CRM",
                "VZ",
                "NFLX",
                "CMCSA",
                "XOM",
                "NKE",
                "INTC",
                "T",
                "AMD",
                "QCOM",
                "TXN",
                "LOW",
                "UPS",
                "HON",
                "SPGI",
                "INTU",
                "CAT",
                "GS",
                "AXP",
                "BKNG",
                "DE",
                "MDT",
                "UNP",
                "RTX",
                "GILD",
                "AMGN",
                "CVS",
                "SCHW",
                "LMT",
                "ADP",
                "MU",
                "LRCX",
                "ADI",
                "ISRG",
                "TJX",
                "C",
                "PYPL",
                "TMUS",
                "BLK",
                "SYK",
                "MDLZ",
                "REGN",
                "NOW",
                "ZTS",
                "MMM",
                "PLD",
                "CB",
                "SO",
                "DUK",
                "BSX",
                "EQIX",
                "APD",
                "CL",
                "ITW",
                "CSX",
                "WM",
                "EMR",
                "NSC",
                "SHW",
                "MCK",
                "GD",
                # NASDAQ major components
                "MSFT",
                "AAPL",
                "AMZN",
                "GOOGL",
                "GOOG",
                "META",
                "TSLA",
                "NVDA",
                "NFLX",
                "ADBE",
                "CRM",
                "PYPL",
                "INTC",
                "AMD",
                "QCOM",
                "AVGO",
                "CSCO",
                "ORCL",
                "TXN",
                "AMAT",
                "MU",
                "LRCX",
                "ADI",
                "MRVL",
                "KLAC",
                "SNPS",
                "CDNS",
                "FTNT",
                "PANW",
                "CRWD",
                "ZS",
                "OKTA",
                "DDOG",
                "SNOW",
                "PLTR",
                "ZM",
                "DOCU",
                "TWLO",
                "SHOP",
                "SQ",
                "ROKU",
                "SPOT",
                "UBER",
                "LYFT",
                "ABNB",
                "DASH",
                "COIN",
                "MRNA",
                "BNTX",
                "GILD",
                "AMGN",
                "BIIB",
                "REGN",
                "VRTX",
                "ISRG",
                "ILMN",
                "INCY",
                "BMRN",
                "ALXN",
                "SGEN",
                "EXAS",
                # Additional popular stocks
                "BABA",
                "JD",
                "PDD",
                "NTES",
                "BILI",
                "IQ",
                "VIPS",
                "WB",
                "TEAM",
                "WDAY",
                "SPLK",
                "VEEV",
                "COUP",
                "ESTC",
                "FROG",
                "NET",
                "FSLY",
                "CFLT",
                "S",
                "WORK",
                "ZOOM",
                "DOCN",
                "RBLX",
                "U",
                "PATH",
                "GTLB",
                "MDB",
                "ATLAS",
                "FIVN",
            }

            all_symbols.update(major_symbols)
            logger.info(f"✅ 添加了{len(major_symbols)}个主要市场股票")

        except ImportError:
            logger.info(
                "yfinance不可用，仅使用现有综合来源"
            )
        except Exception as e:
            logger.warning(f"Yahoo Finance特定股票失败: {e}")

        # Filter to ensure only valid stock symbols
        valid_symbols = set()
        for symbol in all_symbols:
            if (
                symbol
                and isinstance(symbol, str)
                and len(symbol.strip()) >= 1
                and len(symbol.strip()) <= 6  # Allow up to 6 chars for some symbols
                and symbol.strip().replace("-", "").replace(".", "").isalpha()
            ):  # Allow dashes and dots
                valid_symbols.add(symbol.strip().upper())

        logger.info(
            f"📊 Yahoo Finance + 综合总计: {len(valid_symbols)}个有效股票"
        )
        return valid_symbols

    def get_sector_industry_mapping(self) -> Dict[str, Dict[str, str]]:
        """已知股票的综合行业和板块映射"""
        return {
            # Technology - Software & Services
            "AAPL": {"sector": "Technology", "industry": "Consumer Electronics"},
            "MSFT": {"sector": "Technology", "industry": "Software"},
            "GOOGL": {
                "sector": "Communication Services",
                "industry": "Internet Content & Information",
            },
            "GOOG": {
                "sector": "Communication Services",
                "industry": "Internet Content & Information",
            },
            "META": {
                "sector": "Communication Services",
                "industry": "Internet Content & Information",
            },
            "NFLX": {"sector": "Communication Services", "industry": "Entertainment"},
            "ADBE": {"sector": "Technology", "industry": "Software"},
            "CRM": {"sector": "Technology", "industry": "Software"},
            "ORCL": {"sector": "Technology", "industry": "Software"},
            "SNOW": {"sector": "Technology", "industry": "Software"},
            "PLTR": {"sector": "Technology", "industry": "Software"},
            "DDOG": {"sector": "Technology", "industry": "Software"},
            "ZM": {"sector": "Technology", "industry": "Software"},
            "OKTA": {"sector": "Technology", "industry": "Software"},
            "CRWD": {"sector": "Technology", "industry": "Software"},
            "ZS": {"sector": "Technology", "industry": "Software"},
            # Technology - Semiconductors
            "NVDA": {"sector": "Technology", "industry": "Semiconductors"},
            "INTC": {"sector": "Technology", "industry": "Semiconductors"},
            "AMD": {"sector": "Technology", "industry": "Semiconductors"},
            "QCOM": {"sector": "Technology", "industry": "Semiconductors"},
            "AVGO": {"sector": "Technology", "industry": "Semiconductors"},
            "TXN": {"sector": "Technology", "industry": "Semiconductors"},
            "MU": {"sector": "Technology", "industry": "Semiconductors"},
            "AMAT": {
                "sector": "Technology",
                "industry": "Semiconductor Equipment & Materials",
            },
            "LRCX": {
                "sector": "Technology",
                "industry": "Semiconductor Equipment & Materials",
            },
            "KLAC": {
                "sector": "Technology",
                "industry": "Semiconductor Equipment & Materials",
            },
            "ASML": {
                "sector": "Technology",
                "industry": "Semiconductor Equipment & Materials",
            },
            "TSM": {"sector": "Technology", "industry": "Semiconductors"},
            "MRVL": {"sector": "Technology", "industry": "Semiconductors"},
            "MCHP": {"sector": "Technology", "industry": "Semiconductors"},
            "ADI": {"sector": "Technology", "industry": "Semiconductors"},
            # Technology - Hardware & Equipment
            "CSCO": {"sector": "Technology", "industry": "Communication Equipment"},
            "ANET": {"sector": "Technology", "industry": "Communication Equipment"},
            "PANW": {"sector": "Technology", "industry": "Software"},
            # Financial Services
            "JPM": {"sector": "Financial Services", "industry": "Banks"},
            "BAC": {"sector": "Financial Services", "industry": "Banks"},
            "WFC": {"sector": "Financial Services", "industry": "Banks"},
            "C": {"sector": "Financial Services", "industry": "Banks"},
            "GS": {"sector": "Financial Services", "industry": "Capital Markets"},
            "MS": {"sector": "Financial Services", "industry": "Capital Markets"},
            "V": {"sector": "Financial Services", "industry": "Credit Services"},
            "MA": {"sector": "Financial Services", "industry": "Credit Services"},
            "PYPL": {"sector": "Financial Services", "industry": "Credit Services"},
            "AXP": {"sector": "Financial Services", "industry": "Credit Services"},
            "COF": {"sector": "Financial Services", "industry": "Credit Services"},
            "USB": {"sector": "Financial Services", "industry": "Banks"},
            "BX": {"sector": "Financial Services", "industry": "Asset Management"},
            # Healthcare
            "JNJ": {"sector": "Healthcare", "industry": "Drug Manufacturers"},
            "UNH": {"sector": "Healthcare", "industry": "Healthcare Plans"},
            "PFE": {"sector": "Healthcare", "industry": "Drug Manufacturers"},
            "ABBV": {"sector": "Healthcare", "industry": "Drug Manufacturers"},
            "MRK": {"sector": "Healthcare", "industry": "Drug Manufacturers"},
            "TMO": {"sector": "Healthcare", "industry": "Diagnostics & Research"},
            "ABT": {"sector": "Healthcare", "industry": "Medical Devices"},
            "MDT": {"sector": "Healthcare", "industry": "Medical Devices"},
            "DHR": {"sector": "Healthcare", "industry": "Diagnostics & Research"},
            "BMY": {"sector": "Healthcare", "industry": "Drug Manufacturers"},
            "AMGN": {"sector": "Healthcare", "industry": "Biotechnology"},
            "GILD": {"sector": "Healthcare", "industry": "Biotechnology"},
            "BSX": {"sector": "Healthcare", "industry": "Medical Devices"},
            "DXCM": {"sector": "Healthcare", "industry": "Medical Devices"},
            # Consumer Defensive
            "PG": {
                "sector": "Consumer Defensive",
                "industry": "Household & Personal Products",
            },
            "KO": {"sector": "Consumer Defensive", "industry": "Beverages"},
            "PEP": {"sector": "Consumer Defensive", "industry": "Beverages"},
            "WMT": {"sector": "Consumer Defensive", "industry": "Discount Stores"},
            "COST": {"sector": "Consumer Defensive", "industry": "Discount Stores"},
            "TGT": {"sector": "Consumer Defensive", "industry": "Discount Stores"},
            # Consumer Cyclical
            "HD": {
                "sector": "Consumer Cyclical",
                "industry": "Home Improvement Retail",
            },
            "MCD": {"sector": "Consumer Cyclical", "industry": "Restaurants"},
            "NKE": {
                "sector": "Consumer Cyclical",
                "industry": "Footwear & Accessories",
            },
            "SBUX": {"sector": "Consumer Cyclical", "industry": "Restaurants"},
            "LOW": {
                "sector": "Consumer Cyclical",
                "industry": "Home Improvement Retail",
            },
            "TJX": {"sector": "Consumer Cyclical", "industry": "Apparel Retail"},
            # Industrials
            "BA": {"sector": "Industrials", "industry": "Aerospace & Defense"},
            "CAT": {
                "sector": "Industrials",
                "industry": "Farm & Heavy Construction Machinery",
            },
            "GE": {"sector": "Industrials", "industry": "Conglomerates"},
            "MMM": {"sector": "Industrials", "industry": "Conglomerates"},
            "HON": {"sector": "Industrials", "industry": "Conglomerates"},
            "UPS": {
                "sector": "Industrials",
                "industry": "Integrated Freight & Logistics",
            },
            "FDX": {
                "sector": "Industrials",
                "industry": "Integrated Freight & Logistics",
            },
            "LMT": {"sector": "Industrials", "industry": "Aerospace & Defense"},
            "RTX": {"sector": "Industrials", "industry": "Aerospace & Defense"},
            "DE": {
                "sector": "Industrials",
                "industry": "Farm & Heavy Construction Machinery",
            },
            # Energy
            "XOM": {"sector": "Energy", "industry": "Oil & Gas Integrated"},
            "CVX": {"sector": "Energy", "industry": "Oil & Gas Integrated"},
            "COP": {"sector": "Energy", "industry": "Oil & Gas E&P"},
            "EOG": {"sector": "Energy", "industry": "Oil & Gas E&P"},
            "SLB": {"sector": "Energy", "industry": "Oil & Gas Equipment & Services"},
            "PSX": {"sector": "Energy", "industry": "Oil & Gas Refining & Marketing"},
            "VLO": {"sector": "Energy", "industry": "Oil & Gas Refining & Marketing"},
            "MPC": {"sector": "Energy", "industry": "Oil & Gas Refining & Marketing"},
            "OXY": {"sector": "Energy", "industry": "Oil & Gas E&P"},
            "HAL": {"sector": "Energy", "industry": "Oil & Gas Equipment & Services"},
            # Communication Services
            "T": {"sector": "Communication Services", "industry": "Telecom Services"},
            "VZ": {"sector": "Communication Services", "industry": "Telecom Services"},
            "CMCSA": {"sector": "Communication Services", "industry": "Entertainment"},
            "DIS": {"sector": "Communication Services", "industry": "Entertainment"},
            "CHTR": {"sector": "Communication Services", "industry": "Entertainment"},
            "TMUS": {
                "sector": "Communication Services",
                "industry": "Telecom Services",
            },
            # ETFs
            "SPY": {"sector": "Financial Services", "industry": "Asset Management"},
            "QQQ": {"sector": "Financial Services", "industry": "Asset Management"},
            "IWM": {"sector": "Financial Services", "industry": "Asset Management"},
            "VTI": {"sector": "Financial Services", "industry": "Asset Management"},
            "VOO": {"sector": "Financial Services", "industry": "Asset Management"},
            "VEA": {"sector": "Financial Services", "industry": "Asset Management"},
            "VWO": {"sector": "Financial Services", "industry": "Asset Management"},
            "BND": {"sector": "Financial Services", "industry": "Asset Management"},
            "AGG": {"sector": "Financial Services", "industry": "Asset Management"},
            "GLD": {"sector": "Financial Services", "industry": "Asset Management"},
            "SLV": {"sector": "Financial Services", "industry": "Asset Management"},
        }

    @handle_errors(default_return=None, error_category=ErrorCategory.DATA)
    def get_yahoo_finance_classification(self, symbol: str) -> Optional[Dict[str, str]]:
        """从Yahoo Finance API获取分类以获得最大准确性"""
        import threading
        import time

        # Rate limiting
        if not hasattr(self, "_last_request_time"):
            self._last_request_time = 0
            self._request_lock = threading.Lock()

        with self._request_lock:
            current_time = time.time()
            time_since_last = current_time - self._last_request_time
            if time_since_last < 0.2:  # 200ms between requests
                time.sleep(0.2 - time_since_last)
            self._last_request_time = time.time()

        # Try Yahoo Finance quote summary
        url = f"https://query2.finance.yahoo.com/v10/finance/quoteSummary/{symbol}?modules=assetProfile"
        response = requests.get(url, timeout=5)

        if response.status_code == 200:
            data = response.json()
            if "quoteSummary" in data and data["quoteSummary"]["result"]:
                result = data["quoteSummary"]["result"][0]

                if "assetProfile" in result:
                    profile = result["assetProfile"]
                    sector = profile.get("sector", "").strip()
                    industry = profile.get("industry", "").strip()

                    if (
                        sector
                        and industry
                        and sector != "N/A"
                        and industry != "N/A"
                    ):
                        # Normalize sector names
                        sector = self._normalize_sector_name(sector)
                        return {"sector": sector, "industry": industry}

        return None

    def _normalize_sector_name(self, sector: str) -> str:
        """将行业名称标准化为标准格式"""
        sector_mappings = {
            "Technology": "Technology",
            "Healthcare": "Healthcare",
            "Financial Services": "Financial Services",
            "Consumer Cyclical": "Consumer Cyclical",
            "Consumer Defensive": "Consumer Defensive",
            "Industrials": "Industrials",
            "Energy": "Energy",
            "Utilities": "Utilities",
            "Real Estate": "Real Estate",
            "Basic Materials": "Basic Materials",
            "Communication Services": "Communication Services",
            # Alternative names from different sources
            "Information Technology": "Technology",
            "Health Care": "Healthcare",
            "Financials": "Financial Services",
            "Consumer Discretionary": "Consumer Cyclical",
            "Consumer Staples": "Consumer Defensive",
            "Materials": "Basic Materials",
            "Telecommunication Services": "Communication Services",
            "Communication": "Communication Services",
        }

        return sector_mappings.get(sector, sector)

    def is_filtered_security_type(self, ticker: str, config=None) -> bool:
        """
        检查是否为需要过滤的证券类型
        
        过滤的证券类型包括：
        - 认股权证 (W后缀)
        - 特殊证券 (Z后缀) 
        - 单位投资信托 (U后缀)
        - 测试证券 (Y后缀)
        - 优先股某些类别 (PR后缀)
        - 其他已知的低流动性证券
        """
        if not ticker or len(ticker) < 2:
            return False
            
        # 如果没有提供配置，使用默认过滤策略
        if config is None:
            from config import DEFAULT_CONFIG
            config = DEFAULT_CONFIG.ibkr
            
        ticker = ticker.upper().strip()
        
        # 认股权证过滤
        if config.filter_warrants and ticker.endswith('W'):
            return True
            
        # 特殊证券过滤  
        if config.filter_special_securities:
            # Z后缀特殊证券
            if ticker.endswith(('Z', 'U', 'Y')):
                return True
                
            # PR前缀的优先股
            if 'PR' in ticker and len(ticker) > 4:
                return True
                
            # 长度异常的股票代码（通常是临时或特殊证券）
            if len(ticker) > 5:
                return True
                
            # 包含数字的股票代码（通常是临时证券）
            if any(c.isdigit() for c in ticker):
                return True
            
        return False

    def get_security_type(self, ticker: str) -> str:
        """获取证券类型描述"""
        if not ticker:
            return "unknown"
            
        ticker = ticker.upper().strip()
        
        if ticker.endswith('W'):
            return "warrant"  # 认股权证
        elif ticker.endswith('Z'):
            return "special_security"  # 特殊证券
        elif ticker.endswith('U'):
            return "unit_trust"  # 单位投资信托
        elif ticker.endswith('Y'):
            return "test_security"  # 测试证券
        elif 'PR' in ticker:
            return "preferred_stock"  # 优先股
        elif len(ticker) > 5:
            return "long_symbol"  # 异常长符号
        elif any(c.isdigit() for c in ticker):
            return "numeric_symbol"  # 包含数字的符号
        else:
            return "unknown"

    def get_intelligent_classification(self, symbol: str) -> Dict[str, str]:
        """使用模式分析对未知股票进行智能分类"""
        import re

        symbol = symbol.upper().strip()

        # ETF Detection (highest priority)
        etf_patterns = [
            r"^(SPY|QQQ|IWM|VTI|VOO|VEA|VWO|BND|AGG|GLD|SLV|TLT|EFA|EEM)$",
            r"^(XL[A-Z]|IVV|VB|VO|VT|EF|EM|IW|VG|VU|VY)",
            r"ETF$",
            r"^(SPDR|ISHARES|VANGUARD)",
        ]

        for pattern in etf_patterns:
            if re.match(pattern, symbol):
                return {"sector": "Financial Services", "industry": "Asset Management"}

        # Biotechnology patterns (very specific)
        biotech_patterns = [
            r"BIO[A-Z]*$",
            r"GENE[A-Z]*$",
            r"THER[A-Z]*$",
            r"PHARM[A-Z]*$",
            r"[A-Z]*BIO$",
            r"[A-Z]*GENE$",
            r"[A-Z]*THER$",
            r"CLIN[A-Z]*",
            r"DRUG[A-Z]*",
            r"VACC[A-Z]*",
            r"ONCO[A-Z]*",
            r"IMMUN[A-Z]*",
            r"CELL[A-Z]*",
            r"DNA[A-Z]*",
            r"RNA[A-Z]*",
        ]

        for pattern in biotech_patterns:
            if re.match(pattern, symbol):
                return {"sector": "Healthcare", "industry": "Biotechnology"}

        # Technology patterns
        tech_patterns = [
            r"TECH[A-Z]*",
            r"SOFT[A-Z]*",
            r"DATA[A-Z]*",
            r"CYBER[A-Z]*",
            r"CLOUD[A-Z]*",
            r"AI[A-Z]*",
            r"COMP[A-Z]*",
            r"SYS[A-Z]*",
            r"NET[A-Z]*",
            r"WEB[A-Z]*",
            r"DIGI[A-Z]*",
            r"SEMI[A-Z]*",
            r"CHIP[A-Z]*",
            r"MICRO[A-Z]*",
            r"NANO[A-Z]*",
        ]

        for pattern in tech_patterns:
            if re.match(pattern, symbol):
                return {"sector": "Technology", "industry": "Software"}

        # Financial patterns
        financial_patterns = [
            r"BANK[A-Z]*",
            r"[A-Z]*BANK$",
            r"FIN[A-Z]*",
            r"CAP[A-Z]*",
            r"FUND[A-Z]*",
            r"INV[A-Z]*",
            r"LOAN[A-Z]*",
            r"CRED[A-Z]*",
            r"PAY[A-Z]*",
            r"MORT[A-Z]*",
            r"INSUR[A-Z]*",
            r"TRUST[A-Z]*",
        ]

        for pattern in financial_patterns:
            if re.match(pattern, symbol):
                return {"sector": "Financial Services", "industry": "Banks"}

        # Energy patterns
        energy_patterns = [
            r"OIL[A-Z]*",
            r"GAS[A-Z]*",
            r"ENRG[A-Z]*",
            r"PETRO[A-Z]*",
            r"FUEL[A-Z]*",
            r"COAL[A-Z]*",
            r"SOLAR[A-Z]*",
            r"WIND[A-Z]*",
            r"POWER[A-Z]*",
            r"ENERGY[A-Z]*",
            r"DRILL[A-Z]*",
        ]

        for pattern in energy_patterns:
            if re.match(pattern, symbol):
                return {"sector": "Energy", "industry": "Oil & Gas E&P"}

        # Real Estate patterns
        realestate_patterns = [
            r"REIT[A-Z]*",
            r"[A-Z]*REIT$",
            r"REAL[A-Z]*",
            r"PROP[A-Z]*",
            r"LAND[A-Z]*",
            r"BUILD[A-Z]*",
            r"CONST[A-Z]*",
            r"HOME[A-Z]*",
        ]

        for pattern in realestate_patterns:
            if re.match(pattern, symbol):
                return {"sector": "Real Estate", "industry": "REIT"}

        # Healthcare patterns (general)
        healthcare_patterns = [
            r"HEAL[A-Z]*",
            r"MED[A-Z]*",
            r"CARE[A-Z]*",
            r"HOSP[A-Z]*",
            r"SURG[A-Z]*",
            r"DENT[A-Z]*",
            r"DIAG[A-Z]*",
            r"DEVICE[A-Z]*",
        ]

        for pattern in healthcare_patterns:
            if re.match(pattern, symbol):
                return {"sector": "Healthcare", "industry": "Medical Devices"}

        # Symbol suffix analysis
        if symbol.endswith(("R", "RT")):
            return {"sector": "Real Estate", "industry": "REIT"}

        if symbol.endswith(("U", "UN", "UT", "W", "WS", "WT")):
            return {"sector": "Financial Services", "industry": "Capital Markets"}

        # Length-based heuristics (more conservative)
        length = len(symbol)

        if length <= 2:
            # Very short symbols often major established companies
            return {"sector": "Industrials", "industry": "Conglomerates"}
        elif length == 3:
            # 3-letter symbols often established companies - distribute by first letter
            first_letter = symbol[0]
            if first_letter in "ABCDEF":
                return {"sector": "Financial Services", "industry": "Banks"}
            elif first_letter in "GHIJKL":
                return {"sector": "Technology", "industry": "Software"}
            elif first_letter in "MNOPQR":
                return {"sector": "Healthcare", "industry": "Drug Manufacturers"}
            else:
                return {"sector": "Consumer Cyclical", "industry": "Specialty Retail"}
        elif length == 4:
            # 4-letter symbols often tech or biotech
            if any(char.isdigit() for char in symbol):
                return {"sector": "Financial Services", "industry": "Asset Management"}
            else:
                return {"sector": "Technology", "industry": "Software"}
        else:
            # 5+ letter symbols often biotech or specialized
            return {"sector": "Healthcare", "industry": "Biotechnology"}

    def get_default_classification(self, symbol: str) -> Dict[str, str]:
        """使用多种方法获取未知股票的分类"""
        # First try Yahoo Finance API for maximum accuracy
        yahoo_result = self.get_yahoo_finance_classification(symbol)
        if yahoo_result:
            return yahoo_result

        # Fall back to intelligent pattern analysis
        return self.get_intelligent_classification(symbol)

    async def _validate_and_classify_single_symbol(
        self,
        symbol: str,
        sector_mapping: Dict,
        semaphore: asyncio.Semaphore,
        retry_count: int = 2,
    ) -> Optional[Dict]:
        """使用IBKR API验证单个股票并获取精确分类"""

        async with semaphore:  # Limit concurrent requests
            # 首先检查是否为需要过滤的证券类型，避免发送无效的API请求
            from config import DEFAULT_CONFIG
            
            if self.is_filtered_security_type(symbol, DEFAULT_CONFIG.ibkr):
                logger.debug(f"🚫 过滤证券 {symbol} - {self.get_security_type(symbol)}")
                return None
            
            for attempt in range(retry_count):
                try:
                    # Rate limiting for validation requests
                    await asyncio.sleep(0.05)  # Very short delay

                    # First, get contract details for precise classification
                    contract_details = await self.ibkr_client.get_contract_details(
                        symbol
                    )

                    if contract_details:
                        # Extract precise classification from IBKR contract details
                        detail = (
                            contract_details[0]
                            if isinstance(contract_details, list)
                            else contract_details
                        )

                        # Check if stock is from NYSE or NASDAQ only
                        contract = (
                            detail.contract if hasattr(detail, "contract") else detail
                        )
                        exchange = getattr(contract, "primaryExchange", "") or getattr(
                            contract, "exchange", ""
                        )

                        # Filter to only NYSE and NASDAQ stocks
                        if exchange not in [
                            "NYSE",
                            "NASDAQ",
                            "ISLAND",
                        ]:  # ISLAND is NASDAQ's electronic exchange
                            logger.debug(
                                f"Skipping {symbol} - not NYSE/NASDAQ (exchange: {exchange})"
                            )
                            return None

                        # Get classification from IBKR API (keep original precise classifications)
                        category = getattr(detail, "category", "Unknown")
                        industry = getattr(detail, "industry", "Unknown")
                        subcategory = getattr(detail, "subcategory", "Unknown")
                        long_name = getattr(detail, "longName", symbol)

                        # Use IBKR's original precise classifications
                        sector = category if category != "Unknown" else "Unknown"
                        broad_industry = (
                            industry if industry != "Unknown" else "Unknown"
                        )

                        # Validate with historical data to ensure symbol is tradeable
                        df = await self.ibkr_client.get_historical_data(
                            symbol, "5 D", "1 day"
                        )

                        if df is not None and not df.empty:
                            return {
                                "symbol": symbol,
                                "sector": sector,
                                "industry": broad_industry,
                                "subcategory": subcategory,
                                "long_name": long_name,
                                "currency": "USD",
                                "data_source": "IBKR_API",
                                "last_updated": datetime.now().isoformat(),
                            }
                        else:
                            # Contract exists but no historical data - might be delisted
                            logger.debug(
                                f"Contract found for {symbol} but no historical data available"
                            )
                            return None
                    else:
                        # No contract details found - invalid symbol
                        return None

                except Exception as e:
                    if attempt < retry_count - 1:
                        wait_time = 0.5 * (2**attempt)  # Exponential backoff
                        logger.debug(
                            f"Validation attempt {attempt + 1} failed for {symbol}: {e}. Retrying in {wait_time:.1f}s..."
                        )
                        await asyncio.sleep(wait_time)
                    else:
                        logger.debug(
                            f"Symbol {symbol} validation failed after {retry_count} attempts: {e}"
                        )
                        return None

        return None

    async def validate_symbols_with_ibkr_concurrent(
        self, symbols: Set[str], max_concurrent: int = 30
    ) -> Dict:
        """使用IBKR并发请求验证股票以获得最大速度"""
        logger.info(f"🚀 开始对{len(symbols)}个股票进行并发验证")
        logger.info(f"📊 最大并发请求: {max_concurrent}")

        if not await self.ibkr_client.connect():
            logger.error("连接到IBKR失败")
            return {"valid": {}, "invalid": set()}

        symbols_list = list(symbols)
        sector_mapping = self.get_sector_industry_mapping()

        valid_symbols = {}
        invalid_symbols = set()

        # Statistics
        start_time = time.time()
        successful_validations = 0
        failed_validations = 0

        try:
            # Create semaphore to limit concurrent requests
            semaphore = asyncio.Semaphore(max_concurrent)

            # Create tasks for all symbols
            tasks = []
            for symbol in symbols_list:
                task = self._validate_and_classify_single_symbol(
                    symbol, sector_mapping, semaphore
                )
                tasks.append(task)

            # Execute all tasks concurrently with progress reporting
            logger.info("⏳ 执行并发验证...")

            # Process in chunks to provide progress updates
            chunk_size = 500
            total_chunks = (len(tasks) + chunk_size - 1) // chunk_size

            for chunk_idx in range(total_chunks):
                start_idx = chunk_idx * chunk_size
                end_idx = min(start_idx + chunk_size, len(tasks))
                chunk_tasks = tasks[start_idx:end_idx]

                logger.info(
                    f"Processing chunk {chunk_idx + 1}/{total_chunks} ({len(chunk_tasks)} symbols)"
                )

                # Execute chunk concurrently
                chunk_results = await asyncio.gather(
                    *chunk_tasks, return_exceptions=True
                )

                # Process results
                for result in chunk_results:
                    if isinstance(result, Exception):
                        failed_validations += 1
                        continue

                    if result is None:
                        failed_validations += 1
                        continue

                    # Ensure result is a dictionary before accessing
                    if not isinstance(result, dict):
                        failed_validations += 1
                        continue

                    # Valid symbol with precise classification
                    symbol = result["symbol"]
                    valid_symbols[symbol] = {
                        "sector": result["sector"],
                        "industry": result["industry"],
                        "subcategory": result.get("subcategory", "Unknown"),
                        "long_name": result.get("long_name", symbol),
                        "currency": result["currency"],
                        "data_source": result.get("data_source", "IBKR_API"),
                        "last_updated": result.get(
                            "last_updated", datetime.now().isoformat()
                        ),
                    }
                    successful_validations += 1

                # Progress reporting
                elapsed = time.time() - start_time
                processed = successful_validations + failed_validations
                rate = processed / elapsed if elapsed > 0 else 0

                logger.info(
                    f"📈 Progress: {processed}/{len(symbols_list)} symbols, {rate:.1f} symbols/sec"
                )

            # Determine invalid symbols
            valid_symbol_set = set(valid_symbols.keys())
            invalid_symbols = set(symbols_list) - valid_symbol_set

        finally:
            self.ibkr_client.disconnect()

        # Final statistics
        elapsed_time = time.time() - start_time
        success_rate = successful_validations / len(symbols_list) * 100
        validation_rate = len(symbols_list) / elapsed_time if elapsed_time > 0 else 0

        logger.info("🎯 并发验证完成!")
        logger.info(f"✅ 有效: {successful_validations}")
        logger.info(f"❌ 无效: {failed_validations}")
        logger.info(f"📈 成功率: {success_rate:.1f}%")
        logger.info(f"⚡ 验证速率: {validation_rate:.1f} 股票/秒")
        logger.info(f"⏱️  总时间: {elapsed_time:.1f} 秒")

        # Auto-cleanup invalid symbols from symbols_list.txt
        if invalid_symbols:
            self._cleanup_invalid_symbols_from_file(invalid_symbols)

        return {"valid": valid_symbols, "invalid": invalid_symbols}

    def _load_existing_stock_info(self) -> Dict[str, Dict]:
        """加载现有的stock_info.csv以进行增量更新"""
        stock_info_file = "data/stock_info/stock_info.csv"
        existing_data = {}

        if os.path.exists(stock_info_file):
            try:
                import pandas as pd

                df = pd.read_csv(stock_info_file)

                for _, row in df.iterrows():
                    symbol = row["symbol"]

                    # Keep original IBKR classifications (no normalization)
                    existing_data[symbol] = {
                        "sector": row.get("sector", "Unknown"),
                        "industry": row.get("industry", "Unknown"),
                        "subcategory": row.get("subcategory", "Unknown"),
                        "long_name": row.get("long_name", symbol),
                        "currency": row.get("currency", "USD"),
                        "data_source": row.get("data_source", "Unknown"),
                        "last_updated": row.get("last_updated", ""),
                    }

                logger.info(f"加载了{len(existing_data)}个股票的现有数据")
                return existing_data

            except Exception as e:
                logger.warning(f"加载现有stock_info.csv失败: {e}")

        return existing_data

    def _needs_classification_update(
        self, symbol: str, existing_data: Dict, days_threshold: int = 30
    ) -> bool:
        """检查股票是否需要分类更新"""
        if symbol not in existing_data:
            return True

        stock_info = existing_data[symbol]

        # Always update if classification is unknown or incomplete
        if (
            stock_info.get("sector") == "Unknown"
            or stock_info.get("industry") == "Unknown"
            or stock_info.get("data_source") != "IBKR_API"
        ):
            return True

        # Check if data is too old
        last_updated = stock_info.get("last_updated", "")
        if last_updated:
            try:
                from datetime import datetime

                last_update_date = datetime.fromisoformat(
                    last_updated.replace("Z", "+00:00")
                )
                age_days = (datetime.now() - last_update_date).days
                return age_days >= days_threshold
            except Exception:
                return True  # If we can't parse date, update it

        return True  # No last_updated info, needs update

    @async_handle_errors(default_return={"valid": {}, "invalid": set()}, error_category=ErrorCategory.DATA)
    async def validate_and_classify_with_incremental_update(
        self, symbols: Set[str], max_concurrent: int = 30
    ) -> Dict:
        """验证股票并使用增量更新进行分类以最小API调用"""
        logger.info(
            f"🎯 Starting incremental validation and classification of {len(symbols)} symbols"
        )

        # Load existing stock info
        existing_data = self._load_existing_stock_info()

        # Determine which symbols need updates
        symbols_to_update = set()
        symbols_to_keep = {}

        for symbol in symbols:
            if self._needs_classification_update(
                symbol, existing_data, days_threshold=30
            ):
                symbols_to_update.add(symbol)
            else:
                # Keep existing data
                symbols_to_keep[symbol] = existing_data[symbol]

        logger.info("📊 增量更新分析:")
        logger.info(f"   🔄 需要更新: {len(symbols_to_update)}个股票")
        logger.info(f"   ✅ 保持现有: {len(symbols_to_keep)}个股票")
        logger.info(
            f"   📈 API savings: {len(symbols_to_keep)}/{len(symbols)} ({len(symbols_to_keep) / len(symbols) * 100:.1f}%)"
        )

        # Only validate/classify symbols that need updates
        if symbols_to_update:
            logger.info(
                f"🚀 Processing {len(symbols_to_update)} symbols that need updates..."
            )
            validation_results = await self.validate_symbols_with_ibkr_concurrent(
                symbols_to_update, max_concurrent
            )

            # Combine results
            all_valid_symbols = {**symbols_to_keep, **validation_results["valid"]}
            invalid_symbols = validation_results["invalid"]
        else:
            logger.info("✅ 所有股票都有当前数据，不需要API调用!")
            all_valid_symbols = symbols_to_keep
            invalid_symbols = set()

        return {"valid": all_valid_symbols, "invalid": invalid_symbols}

    def _cleanup_invalid_symbols_from_file(self, invalid_symbols: Set[str]):
        """从symbols_list.txt中移除无效股票"""
        symbols_file = self.symbols_file

        if not os.path.exists(symbols_file):
            logger.warning(f"股票文件{symbols_file}未找到，跳过清理")
            return

        try:
            # Read current symbols
            with open(symbols_file, "r") as f:
                current_symbols = set(line.strip() for line in f if line.strip())

            # Remove invalid symbols
            cleaned_symbols = current_symbols - invalid_symbols
            removed_count = len(current_symbols) - len(cleaned_symbols)

            if removed_count > 0:
                # Backup original file
                backup_file = f"backups/{symbols_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                os.makedirs("backups", exist_ok=True)

                import shutil

                shutil.copy2(symbols_file, backup_file)
                logger.info(f"已备份{symbols_file}到{backup_file}")

                # Write cleaned symbols back
                with open(symbols_file, "w") as f:
                    for symbol in sorted(cleaned_symbols):
                        f.write(f"{symbol}\n")

                logger.info(
                    f"🧹 Cleaned up {removed_count} invalid symbols from {symbols_file}"
                )
                logger.info(f"📊 剩余股票: {len(cleaned_symbols)}")

                # Log the removed symbols for reference
                if len(invalid_symbols) <= 20:  # Don't spam log for too many symbols
                    logger.info(
                        f"❌ Removed symbols: {', '.join(sorted(invalid_symbols))}"
                    )
                else:
                    logger.info(
                        f"❌ Removed {len(invalid_symbols)} invalid symbols (too many to list)"
                    )
            else:
                logger.info("✅ 没有无效股票需要从 symbols_list.txt 中移除")

        except Exception as e:
            logger.error(f"清理无效股票失败: {e}")

    @async_handle_errors(default_return={"valid": {}, "invalid": set()}, error_category=ErrorCategory.CONNECTION)
    async def validate_symbols_with_ibkr(
        self, symbols: Set[str], batch_size: int = 100
    ) -> Dict:
        """使用IBKR验证股票 - 根据大小自动选择并发或顺序"""

        if len(symbols) > 50:  # Use concurrent for large batches
            logger.info(f"🚀 使用并发验证对{len(symbols)}个股票进行验证")
            return await self.validate_symbols_with_ibkr_concurrent(
                symbols, max_concurrent=30
            )
        else:
            logger.info(f"📝 使用顺序验证对{len(symbols)}个股票进行验证")
            return await self._validate_symbols_sequential(symbols, 100)

    @async_handle_errors(default_return={"valid": {}, "invalid": set()}, error_category=ErrorCategory.CONNECTION)
    async def _validate_symbols_sequential(
        self, symbols: Set[str], batch_size: int = 100
    ) -> Dict:
        """小批量的顺序验证 (后备方法)"""
        logger.info(f"使用IBKR验证{len(symbols)}个股票 (顺序)...")

        if not await self.ibkr_client.connect():
            logger.error("连接到IBKR失败")
            return {"valid": {}, "invalid": set()}

        symbols_list = list(symbols)
        sector_mapping = self.get_sector_industry_mapping()

        valid_symbols = {}
        invalid_symbols = set()

        try:
            for symbol in symbols_list:
                try:
                    # 首先检查是否为需要过滤的证券类型
                    from config import DEFAULT_CONFIG
                    
                    if self.is_filtered_security_type(symbol, DEFAULT_CONFIG.ibkr):
                        logger.debug(f"🚫 过滤证券 {symbol} - {self.get_security_type(symbol)}")
                        invalid_symbols.add(symbol)
                        continue
                    
                    # Try to get historical data to validate symbol
                    df = await self.ibkr_client.get_historical_data(
                        symbol, "5 D", "1 day"
                    )

                    if df is not None and not df.empty:
                        # Symbol is valid
                        if symbol in sector_mapping:
                            classification = sector_mapping[symbol]
                        else:
                            classification = self.get_default_classification(symbol)

                        valid_symbols[symbol] = {
                            "sector": classification["sector"],
                            "industry": classification["industry"],
                            "currency": "USD",
                        }
                    else:
                        invalid_symbols.add(symbol)

                    # Rate limiting
                    await asyncio.sleep(0.1)

                except Exception as e:
                    invalid_symbols.add(symbol)
                    logger.debug(f"股票{symbol}验证失败: {e}")

        finally:
            self.ibkr_client.disconnect()

        logger.info(
            f"Validation complete: {len(valid_symbols)} valid, {len(invalid_symbols)} invalid"
        )

        # Auto-cleanup invalid symbols from symbols_list.txt
        if invalid_symbols:
            self._cleanup_invalid_symbols_from_file(invalid_symbols)

        return {"valid": valid_symbols, "invalid": invalid_symbols}

    @handle_errors(reraise=True, error_category=ErrorCategory.SYSTEM)
    def update_files(self, valid_symbols: Dict):
        """使用综合分类数据更新stock_info.csv和symbols_list.txt"""
        # Update stock_info.csv with enhanced classification data
        csv_data = []
        for symbol, info in valid_symbols.items():
            csv_data.append(
                {
                    "symbol": symbol,
                    "currency": info.get("currency", "USD"),
                    "sector": info.get("sector", "Unknown"),
                    "industry": info.get("industry", "Unknown"),
                    "subcategory": info.get("subcategory", "Unknown"),
                    "long_name": info.get("long_name", symbol),
                    "data_source": info.get("data_source", "IBKR_API"),
                    "last_updated": info.get(
                        "last_updated", datetime.now().isoformat()
                    ),
                }
            )

        csv_data.sort(key=lambda x: x["symbol"])

        # Enhanced CSV with more classification fields
        fieldnames = [
            "symbol",
            "currency",
            "sector",
            "industry",
            "subcategory",
            "long_name",
            "data_source",
            "last_updated",
        ]

        try:
            # Create backup of existing file
            if os.path.exists(self.stock_info_file):
                backup_file = f"{self.stock_info_file}.backup"
                import shutil

                shutil.copy2(self.stock_info_file, backup_file)
                logger.info(f"已创建备份: {backup_file}")

            # Write new stock info file
            with open(self.stock_info_file, "w", newline="", encoding="utf-8") as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(csv_data)

            logger.info(
                f"Updated {self.stock_info_file} with {len(csv_data)} symbols and precise classifications"
            )

        except Exception as e:
            logger.error(f"写入stock_info.csv失败: {e}")
            # Restore from backup if available
            backup_file = f"{self.stock_info_file}.backup"
            if os.path.exists(backup_file):
                import shutil

                shutil.copy2(backup_file, self.stock_info_file)
                logger.info("从备份恢复")
            raise

        try:
            # Create backup of symbols file
            if os.path.exists(self.symbols_file):
                backup_file = f"{self.symbols_file}.backup"
                import shutil

                shutil.copy2(self.symbols_file, backup_file)

            # Update symbols_list.txt
            sorted_symbols = sorted(valid_symbols.keys())

            with open(self.symbols_file, "w") as f:
                f.write(" ".join(sorted_symbols) + "\n")

            logger.info(
                f"Updated {self.symbols_file} with {len(sorted_symbols)} symbols"
            )

        except Exception as e:
            logger.error(f"写入symbols_list.txt失败: {e}")
            # Restore from backup if available
            backup_file = f"{self.symbols_file}.backup"
            if os.path.exists(backup_file):
                import shutil

                shutil.copy2(backup_file, self.symbols_file)
                logger.info("从备份恢复股票文件")
            raise

    @handle_errors(reraise=True, error_category=ErrorCategory.SYSTEM)
    def generate_update_report(self, results: Dict):
        """生成综合更新报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"stock_universe_update_report_{timestamp}.txt"

        with open(report_file, "w") as f:
            f.write("STOCK UNIVERSE UPDATE REPORT\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("SUMMARY:\n")
            f.write(f"Valid symbols: {len(results['valid'])}\n")
            f.write(f"Invalid symbols: {len(results['invalid'])}\n")
            f.write(
                f"Success rate: {len(results['valid']) / (len(results['valid']) + len(results['invalid'])) * 100:.1f}%\n\n"
            )

            # Sector breakdown
            sector_counts = {}
            for symbol, info in results["valid"].items():
                sector = info["sector"]
                sector_counts[sector] = sector_counts.get(sector, 0) + 1

            f.write("SECTOR BREAKDOWN:\n")
            for sector, count in sorted(
                sector_counts.items(), key=lambda x: x[1], reverse=True
            ):
                f.write(f"{sector}: {count} symbols\n")
            f.write("\n")

            f.write("FILES UPDATED:\n")
            f.write(
                f"- {self.stock_info_file}: {len(results['valid'])} symbols with sector/industry data\n"
            )
            f.write(
                f"- {self.symbols_file}: {len(results['valid'])} symbols for trading system\n\n"
            )

            if results["invalid"]:
                f.write("INVALID SYMBOLS (first 50):\n")
                for symbol in sorted(list(results["invalid"])[:50]):
                    f.write(f"{symbol}\n")

        logger.info(f"生成更新报告: {report_file}")

    @async_handle_errors(reraise=True, error_category=ErrorCategory.SYSTEM)
    async def run_full_update(self, max_symbols: Optional[int] = None):
        """使用Yahoo Finance和综合源运行完整的股票全集更新"""
        logger.info("开始综合股票全集更新...")
        logger.info(
            "🚀 YAHOO FINANCE MODE: Using Yahoo Finance + comprehensive sources for NYSE/NASDAQ stocks"
        )
        logger.info("📈 主要来源: Yahoo Finance和外部API")
        logger.info("🎯 获取完整的NYSE和NASDAQ股票全集")
        logger.info("=" * 60)

        # Backup existing files
        self.backup_existing_files()

        # Get comprehensive symbol list (NEW: Comprehensive IBKR scanner + fallback sources)
        logger.info("🔍 阶段1: 综合股票收集...")
        all_symbols = await self.get_enhanced_symbol_sources()

        # Limit symbols if specified (for testing)
        if max_symbols and len(all_symbols) > max_symbols:
            logger.info(f"为此次更新限制为{max_symbols}个股票")
            all_symbols = set(list(all_symbols)[:max_symbols])

        # Validate symbols with IBKR and get precise classifications (with incremental updates)
        logger.info("🔍 阶段2: 股票验证和分类...")
        results = await self.validate_and_classify_with_incremental_update(
            all_symbols, max_concurrent=30
        )

        # Update files
        logger.info("📝 阶段3: 更新股票全集文件...")
        self.update_files(results["valid"])

        # Generate report
        logger.info("📊 阶段4: 生成更新报告...")
        self.generate_update_report(results)

        logger.info("=" * 60)
        logger.info("✅ 股票全集更新完成!")
        logger.info(f"📊 总有效股票: {len(results['valid'])}")
        logger.info(f"📁 更新的文件: {self.stock_info_file}, {self.symbols_file}")
        logger.info(
            "🎯 Data sourced from Yahoo Finance + comprehensive external sources"
        )
        logger.info("📈 完成NYSE和NASDAQ股票全覆盖")


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="Stock Universe Updater")
    parser.add_argument(
        "--max-symbols", type=int, help="Maximum symbols to process (for testing)"
    )
    parser.add_argument(
        "--full-update", action="store_true", help="Run full universe update"
    )

    args = parser.parse_args()

    updater = StockUniverseUpdater()

    if args.full_update:
        await updater.run_full_update(args.max_symbols)
    else:
        print("使用 --full-update 刷新完整的股票池")
        print(
            "Example: python update_stock_universe.py --full-update --max-symbols 1000"
        )


if __name__ == "__main__":
    asyncio.run(main())
