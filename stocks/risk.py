#!/usr/bin/env python3
"""智能止损系统

基于相关性的动态止损管理系统，使用机器学习模型预测和股票相关性
来设置智能止损订单。提供多种止损策略、风险预警和自动执行功能。
集成IBKR API实现实时仓位监控和自动止损执行。
"""

import logging
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Tuple

import numpy as np

from core.broker import IBKRClient
from config import DEFAULT_CONFIG, IBKRConfig, get_config
from core.exceptions import (
    TradingSystemError,
    ErrorSeverity,
    ErrorCategory,
    OperationResult,
    IBKRConnectionError,
    DataValidationError,
    ErrorCategory,
    handle_errors,
    async_handle_errors,
    safe_execute,
    OperationResult,
    safe_execute
)

logger = logging.getLogger(__name__)


class StopLossType(Enum):
    """止损订单类型"""

    TRAILING = "trailing"
    FIXED = "fixed"
    CORRELATION_BASED = "correlation_based"
    VOLATILITY_ADJUSTED = "volatility_adjusted"


@dataclass
class StopLossOrder:
    """止损订单配置"""

    ticker: str
    order_type: StopLossType
    stop_price: float
    trail_amount: Optional[float] = None
    correlation_factor: Optional[float] = None
    volatility_factor: Optional[float] = None
    created_at: datetime = None
    last_updated: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.last_updated is None:
            self.last_updated = datetime.now()


@dataclass
class PositionInfo:
    """用于止损计算的仓位信息"""

    ticker: str
    units: int
    purchase_price: float
    current_price: float
    sector: str
    industry: str
    trend_score: float
    predicted_price: float
    volatility: float
    correlation_matches: List[str]
    purchase_date: datetime


class SmartStopLossSystem:
    """
    智能止损系统，使用：
    1. 机器学习模型预测动态目标
    2. 股票相关性进行风险管理
    3. 板块/行业分析提供背景
    4. 波动率调整阈值
    """

    def __init__(self, ibkr_config: IBKRConfig = None):
        if ibkr_config:
            self.config = ibkr_config
        else:
            # 从UnifiedConfig中提取IBKRConfig
            unified_config = DEFAULT_CONFIG
            self.config = unified_config.ibkr

        # 为风险管理模块使用不同的客户端ID，避免冲突
        risk_config = IBKRConfig(
            host=self.config.host,
            port=self.config.port,
            client_id=500,  # 使用专用的客户端ID
            paper_trading=self.config.paper_trading,
            max_position_size=self.config.max_position_size,
            max_daily_trades=self.config.max_daily_trades,
            max_portfolio_risk=self.config.max_portfolio_risk,
            stop_loss_pct=self.config.stop_loss_pct,
            take_profit_pct=self.config.take_profit_pct,
        )
        self.ibkr_client = IBKRClient(risk_config)
        self.active_orders: Dict[str, StopLossOrder] = {}
        self.position_history: Dict[str, List[PositionInfo]] = {}

        # 配置参数
        self.base_stop_loss_pct = 0.15  # 15% 基础止损
        self.base_take_profit_pct = 0.25  # 25% 基础止盈
        self.correlation_weight = 0.3  # 相关性调整权重
        self.volatility_weight = 0.2  # 波动率调整权重
        self.trend_weight = 0.4  # 趋势分数调整权重
        self.sector_correlation_threshold = 0.7  # 板块相关性阈值

    async def connect(self) -> bool:
        """连接到 IBKR"""
        return await self.ibkr_client.connect()

    def disconnect(self):
        """断开与 IBKR 的连接"""
        self.ibkr_client.disconnect()

    def calculate_smart_stop_loss(
        self, position: PositionInfo, correlation_data: Dict, market_data: Dict
    ) -> Tuple[float, float]:
        """
        计算智能止损和止盈水平

        返回:
            Tuple[止损价格, 止盈价格]
        """

        # 基础计算
        base_stop = position.purchase_price * (1 - self.base_stop_loss_pct)
        base_profit = position.purchase_price * (1 + self.base_take_profit_pct)

        # 1. 趋势分数调整
        trend_adjustment = self._calculate_trend_adjustment(position.trend_score)

        # 2. Volatility Adjustment
        volatility_adjustment = self._calculate_volatility_adjustment(
            position.volatility
        )

        # 3. Correlation Adjustment
        correlation_adjustment = self._calculate_correlation_adjustment(
            position, correlation_data, market_data
        )

        # 4. Sector Performance Adjustment
        sector_adjustment = self._calculate_sector_adjustment(position, market_data)

        # 结合所有调整
        total_stop_adjustment = (
            trend_adjustment * self.trend_weight
            + volatility_adjustment * self.volatility_weight
            + correlation_adjustment * self.correlation_weight
            + sector_adjustment * 0.1
        )

        total_profit_adjustment = (
            total_stop_adjustment * 0.5
        )  # 在盈利方面更保守

        # 应用调整
        adjusted_stop = base_stop * (1 + total_stop_adjustment)
        adjusted_profit = base_profit * (1 + total_profit_adjustment)

        # 确保止损低于当前价格，止盈高于当前价格
        final_stop = min(adjusted_stop, position.current_price * 0.95)
        final_profit = max(adjusted_profit, position.current_price * 1.05)

        logger.info(f"为{position.ticker}计算止损:")
        logger.info(f"  基础止损: ${base_stop:.2f}, 调整后: ${final_stop:.2f}")
        logger.info(f"  基础止盈: ${base_profit:.2f}, 调整后: ${final_profit:.2f}")
        logger.info(
            f"  调整 - 趋势: {trend_adjustment:.3f}, 波动率: {volatility_adjustment:.3f}, 相关性: {correlation_adjustment:.3f}"
        )

        return final_stop, final_profit

    def _calculate_trend_adjustment(self, trend_score: float) -> float:
        """
        Calculate adjustment based on ML model trend score
        Negative trend score (below trend) = tighter stop loss
        Positive trend score (above trend) = looser stop loss
        """
        if trend_score < -2.0:  # 高度低于趋势 - 非常紧密的止损
            return -0.3
        elif trend_score < -1.0:  # 低于趋势 - 紧密止损
            return -0.15
        elif trend_score < 1.0:  # 沿趋势 - 正常止损
            return 0.0
        elif trend_score < 2.0:  # 高于趋势 - 宽松止损
            return 0.15
        else:  # 高度高于趋势 - 非常宽松止损
            return 0.3

    def _calculate_volatility_adjustment(self, volatility: float) -> float:
        """
        Calculate adjustment based on stock volatility
        High volatility = wider stop loss to avoid noise
        Low volatility = tighter stop loss
        """
        if volatility > 0.4:  # Very high volatility
            return 0.25
        elif volatility > 0.25:  # High volatility
            return 0.15
        elif volatility > 0.15:  # Medium volatility
            return 0.0
        elif volatility > 0.1:  # Low volatility
            return -0.1
        else:  # Very low volatility
            return -0.2

    def _calculate_correlation_adjustment(
        self, position: PositionInfo, correlation_data: Dict, market_data: Dict
    ) -> float:
        """
        Calculate adjustment based on correlated stocks performance
        If correlated stocks are performing well = looser stop
        If correlated stocks are performing poorly = tighter stop
        """
        if not position.correlation_matches:
            return 0.0

        correlation_performance = []

        for corr_ticker in position.correlation_matches:
            if corr_ticker in market_data:
                corr_data = market_data[corr_ticker]
                # Calculate performance of correlated stock
                perf = (
                    corr_data["current_price"] - corr_data["predicted_price"]
                ) / corr_data["predicted_price"]
                correlation_performance.append(perf)

        if not correlation_performance:
            return 0.0

        avg_corr_performance = np.mean(correlation_performance)

        # If correlated stocks are outperforming predictions, be more optimistic
        if avg_corr_performance > 0.05:  # 5% outperformance
            return 0.2
        elif avg_corr_performance > 0.02:  # 2% outperformance
            return 0.1
        elif avg_corr_performance < -0.05:  # 5% underperformance
            return -0.2
        elif avg_corr_performance < -0.02:  # 2% underperformance
            return -0.1
        else:
            return 0.0

    def _calculate_sector_adjustment(
        self, position: PositionInfo, market_data: Dict
    ) -> float:
        """
        Calculate adjustment based on sector performance
        """
        sector_stocks = [
            ticker
            for ticker, data in market_data.items()
            if data.get("sector") == position.sector and ticker != position.ticker
        ]

        if len(sector_stocks) < 3:  # Not enough sector data
            return 0.0

        sector_performance = []
        for ticker in sector_stocks[:10]:  # Limit to 10 stocks for performance
            data = market_data[ticker]
            perf = (data["current_price"] - data["predicted_price"]) / data[
                "predicted_price"
            ]
            sector_performance.append(perf)

        avg_sector_performance = np.mean(sector_performance)

        # Sector momentum adjustment
        if avg_sector_performance > 0.03:
            return 0.1
        elif avg_sector_performance < -0.03:
            return -0.1
        else:
            return 0.0

    async def create_stop_loss_orders(
        self, positions: List[PositionInfo], correlation_data: Dict, market_data: Dict
    ) -> Dict[str, StopLossOrder]:
        """
        Create intelligent stop loss orders for all positions
        """
        new_orders = {}

        for position in positions:
            try:
                self._create_single_stop_loss(position, correlation_data, market_data)
                result = OperationResult.success_result(data=True)
            except Exception as e:
                logger.error(f"创建止损订单失败 {position.ticker}: {e}")
                result = OperationResult.error_result(error=TradingSystemError(
                    message=f"创建止损订单失败: {e}",
                    category=ErrorCategory.TRADING,
                    severity=ErrorSeverity.MEDIUM
                ))
                continue

            if result.success:
                stop_price, profit_price = self.calculate_smart_stop_loss(
                    position, correlation_data, market_data
                )

                # Determine best stop loss type based on position characteristics
                stop_type = self._determine_stop_loss_type(position)

                # Create stop loss order
                order = StopLossOrder(
                    ticker=position.ticker,
                    order_type=stop_type,
                    stop_price=stop_price,
                    trail_amount=self._calculate_trail_amount(position)
                    if stop_type == StopLossType.TRAILING
                    else None,
                    correlation_factor=self._calculate_correlation_factor(
                        position, correlation_data
                    ),
                    volatility_factor=position.volatility,
                )

                new_orders[position.ticker] = order

                # Place the actual order with IBKR
                if await self._place_stop_loss_order(order, position):
                    logger.info(
                        f"✅ 已为{position.ticker}下止损订单，价格${stop_price:.2f}"
                    )
                else:
                    logger.error(
                        f"❌ 为{position.ticker}下止损订单失败"
                    )



        self.active_orders.update(new_orders)
        return new_orders

    def _create_single_stop_loss(self, position: PositionInfo, correlation_data: Dict, market_data: Dict) -> None:
        """为单个仓位创建止损订单的内部方法"""
        # 这个方法目前只是一个占位符，实际的止损创建逻辑在主循环中处理
        # 可以在这里添加额外的验证或预处理逻辑
        logger.debug(f"准备为 {position.ticker} 创建止损订单")

    def _determine_stop_loss_type(self, position: PositionInfo) -> StopLossType:
        """为仓位确定最佳止损类型"""
        if position.volatility > 0.3:
            return StopLossType.VOLATILITY_ADJUSTED
        elif len(position.correlation_matches) > 2:
            return StopLossType.CORRELATION_BASED
        elif position.trend_score > 1.0:  # Strong uptrend
            return StopLossType.TRAILING
        else:
            return StopLossType.FIXED

    def _calculate_trail_amount(self, position: PositionInfo) -> float:
        """根据波动性计算跟踪金额"""
        base_trail = position.current_price * 0.05  # 5% base trail
        volatility_adjustment = position.volatility * 0.1
        return base_trail * (1 + volatility_adjustment)

    def _calculate_correlation_factor(
        self, position: PositionInfo, correlation_data: Dict
    ) -> float:
        """计算相关性强度因子"""
        if not position.correlation_matches:
            return 0.0
        return min(len(position.correlation_matches) / 5.0, 1.0)  # Max factor of 1.0

    async def _place_stop_loss_order(
        self, order: StopLossOrder, position: PositionInfo
    ) -> bool:
        """在IBKR上下实际止损订单"""
        try:
            # Use IBKR client to place stop loss order
            result = await self.ibkr_client.place_stop_loss_order(
                symbol=order.ticker,
                quantity=position.units,
                stop_price=order.stop_price,
                action="SELL",
            )
            return result is not None
        except Exception as e:
            logger.error(f"为{order.ticker}下止损订单失败: {e}")
            return False

    async def update_stop_loss_orders(
        self,
        current_positions: List[PositionInfo],
        correlation_data: Dict,
        market_data: Dict,
    ):
        """根据新市场数据更新现有止损订单"""
        for position in current_positions:
            if position.ticker in self.active_orders:
                order = self.active_orders[position.ticker]

                # Recalculate stop loss
                new_stop, new_profit = self.calculate_smart_stop_loss(
                    position, correlation_data, market_data
                )

                # Update if significant change
                if (
                    abs(new_stop - order.stop_price) / order.stop_price > 0.05
                ):  # 5% change threshold
                    await self._modify_stop_loss_order(order, new_stop)
                    order.stop_price = new_stop
                    order.last_updated = datetime.now()

    async def _modify_stop_loss_order(
        self, order: StopLossOrder, new_stop_price: float
    ):
        """修改现有止损订单"""
        try:
            await self.ibkr_client.modify_stop_loss_order(order.ticker, new_stop_price)
            logger.info(
                f"将{order.ticker}的止损更新为${new_stop_price:.2f}"
            )
        except Exception as e:
            logger.error(f"修改{order.ticker}的止损失败: {e}")

    def get_stop_loss_summary(self) -> Dict:
        """获取所有活跃止损订单的摘要"""
        summary = {
            "total_orders": len(self.active_orders),
            "orders_by_type": {},
            "orders": [],
        }

        for order in self.active_orders.values():
            order_type = order.order_type.value
            summary["orders_by_type"][order_type] = (
                summary["orders_by_type"].get(order_type, 0) + 1
            )
            summary["orders"].append(
                {
                    "ticker": order.ticker,
                    "type": order_type,
                    "stop_price": order.stop_price,
                    "created": order.created_at,
                    "updated": order.last_updated,
                }
            )

        return summary
